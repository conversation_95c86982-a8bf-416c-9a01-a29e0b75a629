import Iconify from '@purge-icons/generated';
<script setup lang="ts">
import { BaseButton } from '@/components/Button'
import { ref } from 'vue'
import { genFileId, ElUpload } from 'element-plus'
import type { UploadInstance, UploadFile, UploadProps, UploadRawFile } from 'element-plus'
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()
const upload = ref<UploadInstance>()
const files = ref<UploadFile[]>([])
const model = defineModel<Nullable<UploadFile>>({
  type: Object as PropType<Nullable<UploadFile>>,
  default: () => undefined
})
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}

const handleChange = (type: 'change' | 'remove' = 'change', file: UploadFile) => {
  if (type === 'remove') {
    model.value = null
  } else {
    model.value = file
  }
}
watch(
  () => model.value,
  (val) => {
    if (!val) {
      files.value = []
    } else {
      files.value = [val]
    }
  }
)
</script>
<template>
  <ElUpload
    ref="upload"
    class="w-full"
    :file-list="files"
    :limit="1"
    :on-exceed="handleExceed"
    :auto-upload="false"
    :on-change="(file) => handleChange('change', file)"
    :on-remove="(file) => handleChange('remove', file)"
  >
    <template #trigger>
      <BaseButton type="default"
        ><Icon icon="ant-design:upload-outlined" />{{ t('上传文件') }}</BaseButton
      >
    </template>
  </ElUpload>
</template>
