import request from '@/axios'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, UserType } from './types'
import { AxiosHeaders } from 'axios'

const BASE_URL = '/auth'
export const getProfile = async (): Promise<IResponse<Recordable>> => {
  return await request.get({ url: `${BASE_URL}/profile` })
}

export const changeLangApi = async (code: string) => {
  return await request.post({ url: `${BASE_URL}/lang/${code}` })
}

export const getLangs = (): Promise<IResponse<Lang[]>> => {
  return request.get({ url: `${BASE_URL}/langs` })
}

export const getToken = async (authKey: string) => {
  const { data } = await request.get<boolean>({
    url: `${BASE_URL}/token`,
    headers: AxiosHeaders.from({ 'auth-key': authKey })
  })
  return data
}

export const getPermissions = async (): Promise<
  IResponse<{
    routes: SysRouter[]
    buttons: string[]
  }>
> => {
  return await request.get({ url: `${BASE_URL}/permissions` })
}
export const loginByTokenApi = async (
  token: string | null
): Promise<
  IResponse<{
    token: string
    user: { id: string; name: string; username: string; avatar?: string }
  }>
> => {
  return await request.post({ url: `${BASE_URL}/login`, data: { token } })
}

export const loginDevApi = async () => {
  return await request.post<{ access_token: string }>({
    url: 'http://www.kevinguo.top:3000/admin-api/auth/login',
    data: {
      username: 'admin',
      password: 'e10adc3949ba59abbe56e057f20f883e'
    }
  })
}
export const loginApi = (data: UserType): Promise<IResponse<UserType>> => {
  return request.post({ url: '/mock/user/login', data })
}

export const loginOutApi = (): Promise<IResponse> => {
  return request.get({ url: `${BASE_URL}/logout` })
}

export const getUserListApi = ({ params }: AxiosConfig) => {
  return request.get<{
    code: string
    data: {
      list: UserType[]
      total: number
    }
  }>({ url: '/api/user/list', params })
}
export const getDepartmentListApi = ({ params }: AxiosConfig) => {
  return request.get<{
    code: string
    data: {
      list: UserType[]
      total: number
    }
  }>({ url: '/api/user/department/list', params })
}

export const getAdminRoleApi = (params: any): Promise<IResponse<AppCustomRouteRecordRaw[]>> => {
  return request.get({ url: '/api/role/list', params })
}

export const getTestRoleApi = (params: any): Promise<IResponse<string[]>> => {
  return request.get({ url: '/api/role/list2', params })
}
