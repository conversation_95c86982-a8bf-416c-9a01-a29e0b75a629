<script setup lang="ts">
import { getBadTypeChartApi } from '@/api/dashboard'
import { SearchParams } from '@/api/dashboard/types'
import { Echart } from '@/components/Echart'
import { useI18n } from '@/hooks/web/useI18n'
import { useDashboardStore } from '@/store/modules/dashboard'
import dayjs from 'dayjs'
import { EChartsOption, EChartsType } from 'echarts'
import { DictionaryCategory } from '@/api/dictionary/types'

const props = defineProps<{
  start: Date
  end: Date
  loading: { status: boolean }
}>()
const store = useDashboardStore()
const $emit = defineEmits(['update:loading'])

const echartRef = ref<{
  getECharts: () => EChartsType
  dispose: () => void
} | null>(null)
const { t } = useI18n()
$emit('update:loading', { status: true })

watchEffect(async () => {
  const { data } = await getBadTypeChartApi({
    ...store.filter,
    monthRange: [props.start, props.end]
  } as SearchParams)
  setTimeout(() => {
    modifyOptions.value = data.data
  }, 100)
})
const options = ref<EChartsOption>({
  tooltip: {
    formatter: (params) => {
      return `${t(params.seriesName)}<br/>${params.marker} <span style='margin-right:1rem'>${t(
        params.name
      )}</span><b>${params.value}</b>`
    }
  },
  yAxis: {}
})
const modifyOptions = computed<Record<string, any>>({
  get: () => options.value,
  set: (data: { month: string; type: string; value: string }[]) => {
    const legend = store.getOptions(DictionaryCategory.UNQUALITY_TYPE)
    const itemStyles = [
      {
        color: 'rgb(0, 157, 255)'
      },
      {
        color: 'rgb(34, 228, 255)'
      },
      {
        color: 'rgb(59, 255, 208)'
      },
      {
        color: 'rgb(4, 227, 138)'
      },
      {
        color: 'rgb(157, 255, 134)'
      },
      {
        color: 'rgb(254, 229, 136)'
      }
    ]
    const startMonth = dayjs(props.start)
    const endMonth = dayjs(props.end)
    const xAxisData: string[] = []
    const monthRange: string[] = []
    const diffYear = props.start.getFullYear() !== props.end.getFullYear()
    for (let m = startMonth; !m.isAfter(endMonth); m = m.add(1, 'months')) {
      monthRange.push(m.format('YYYY-MM'))
      if (diffYear) {
        xAxisData.push(m.format('YYYY-MM'))
      } else {
        xAxisData.push(m.format('M月'))
      }
    }
    const emphasisStyle = {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0,0,0,0.3)'
      }
    }

    options.value.legend = {
      data: legend,
      type: 'scroll',
      left: '10%',
      top: '5%',
      formatter: (name) => t(name)
    }
    options.value.xAxis = {
      data: xAxisData,
      axisLine: { onZero: true },
      splitLine: { show: false },
      splitArea: { show: false },
      triggerEvent: true,
      axisLabel: diffYear ? { rotate: 45 } : { rotate: 0 }
    }
    options.value.grid = {
      //      bottom: diffYear ? 86 : 66
      bottom: diffYear ? 48 : 30
    }
    options.value.series = legend.map((name, index) => {
      return {
        name,
        type: 'bar',
        stack: 'one',
        emphasis: emphasisStyle,
        data: monthRange.map((month) => {
          return data.find((datum) => datum.month === month && datum.type === name)?.value ?? 0
        }),
        itemStyle: itemStyles[index]
      }
    })
    const _echartRef = unref(echartRef)
    if (!!_echartRef) {
      const echarts = _echartRef.getECharts()
      echarts.setOption(options.value, true)
    }
  }
})
watch(
  () => echartRef.value,
  (echartRef) => {
    if (echartRef) {
      const echarts = echartRef.getECharts()
      echarts.on('click', function ({ seriesName, name, value }) {
        console.log(11111111)
        if (value === 0) {
          return
        }
        store.seriesFilter = { unqualityType: [seriesName!] }
        if (name.length > 4) {
          store.monthRange = [
            dayjs(name, 'YYYY-MM').startOf('M').toDate(),
            dayjs(name, 'YYYY-MM').endOf('M').toDate()
          ]
        } else {
          const year = dayjs(props.start).year()
          store.monthRange = [
            dayjs(`${year}-${name}`, 'YYYY-M月').startOf('M').toDate(),
            dayjs(`${year}-${name}`, 'YYYY-M月').endOf('M').toDate()
          ]
        }

        store.setTableDialogVisible(true)
      })
    }
  },
  { deep: true }
)
</script>
<template>
  <Echart
    ref="echartRef"
    v-if="options.legend"
    :options="options as EChartsOption"
    height="18.75rem"
  />
</template>
