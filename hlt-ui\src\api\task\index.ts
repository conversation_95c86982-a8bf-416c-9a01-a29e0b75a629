import { AxiosHeaders } from 'axios'
import { UploadFile } from 'element-plus'
import { saveAs } from 'file-saver'
import request from '../../axios'
import { AxiosResponse } from '../../axios/types'
import type { ID, Page } from '../base/types'
import { Reason, ReasonDetail, type Problem } from '../problem/types'
import { PageRequest } from './../../utils/index'
const BASE_API = '/task'

export const getListApi = async (data: {
  query?: Record<string, any>
  page?: { pageNo: number; pageSize: number }
  sort?: { prop: string; order: 'ascending' | 'descending' | null }
}) => {
  return request.post<Page<Problem>>({
    url: `${BASE_API}/list`,
    data: new PageRequest(data)
  })
}

export const getApi = async (id: ID) => {
  return request.get<Problem>({ url: `${BASE_API}/${id}` })
}

export const updateReasonApi = async (id: ID, reason: Partial<Reason>) => {
  return request.put<Reason[]>({ url: `${BASE_API}/${id}/reason`, data: reason })
}

export const submitApi = async (id: ID, data?: { approved: boolean; remark?: string }) => {
  return request.post<Problem>({ url: `${BASE_API}/submit/${id}`, data })
}

export const saveOrUpdateDetail = async (id: ID, detail: ReasonDetail, file?: UploadFile) => {
  const form = new FormData()
  form.append('entity', JSON.stringify(detail))
  if (!!file?.raw) {
    form.append('file', file.raw, file.name)
  }

  return request.post<Problem>({
    url: `${BASE_API}/${id}/reason-detail`,
    data: form,
    headers: AxiosHeaders.from().setContentType('multipart/form-data')
  })
}

export const deleteDetail = async (id: ID, ids: ID[]) => {
  return request.delete({ url: `${BASE_API}/${id}/reason-detail`, data: { ids } })
}

export const previewApi = async (id: ID, key: string) => {
  const { data, headers } = (await request.get({
    url: `${BASE_API}/attachment/${id}/${key}`,
    responseType: 'blob'
  })) as unknown as AxiosResponse
  let fileName
  if (!!headers) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(headers['content-disposition'])
    fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
  }
  saveAs(data, fileName)
}

export const getReasonListApi = async (id: ID) => {
  return request.get<Reason[]>({ url: `${BASE_API}/${id}/reason` })
}

export const saveReasonApi = async (id: ID, reason: Partial<Reason>) => {
  return request.post<Reason[]>({ url: `${BASE_API}/${id}/reason`, data: reason })
}

export const transferReasonApi = async (id: ID, reason: Partial<Reason>) => {
  return request.post<Reason[]>({ url: `${BASE_API}/${id}/transfer`, data: reason })
}
