import request from '@/axios'
import type { ID, Page, Result } from '../base/types'
import { PageRequest } from './../../utils/index'
import type { CodeRule } from './types'

const BASE_API = '/code-rule'

export const getListApi = async (data: {
  query?: Record<string, any>
  page?: { pageNo: number; pageSize: number }
  sort?: { prop: string; order: 'ascending' | 'descending' | null }
}) => {
  return request.post<Page<CodeRule>>({
    url: `${BASE_API}/list`,
    data: new PageRequest(data)
  })
}

export const saveOrUpdateApi = async (entity: CodeRule) => {
  return request.post<Result>({ url: `${BASE_API}/`, data: entity })
}

export const deleteApi = async (ids: ID[]) => {
  return request.delete<Result>({ url: `${BASE_API}/`, data: { ids } })
}

export const duplicateApi = async (entity: Partial<CodeRule>) => {
  return request.post<boolean>({ url: `${BASE_API}/duplicate`, data: entity })
}
