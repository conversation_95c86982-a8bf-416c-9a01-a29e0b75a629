<script setup lang="tsx">
import { getPreviewURLApi } from '@/api/problem'
import { Problem } from '@/api/problem/types'
import { Descriptions, DescriptionsSchema } from '@/components/Descriptions'
import { useI18n } from '@/hooks/web/useI18n'
import { propTypes } from '@/utils/propTypes'
import { ElCol, ElCollapse, ElCollapseItem, ElImage, ElRow, ElSpace } from 'element-plus'
import Log from './Log.vue'
import Reason from './Reason.vue'

const { t } = useI18n()

defineProps({
  actionType: propTypes.string.def('edit'),
  currentRow: {
    type: Object as PropType<Nullable<Partial<Problem>>>,
    default: () => null
  }
})
const schema = reactive<DescriptionsSchema[]>([
  { field: 'code', label: t('问题编号') },
  {
    field: 'createdOn',
    label: t('创建日期')
  },
  {
    field: 'businessUnit',
    label: t('事业部'),
    slots: {
      default: ({ businessUnit }) => {
        return <>{t(businessUnit)}</>
      }
    }
  },
  {
    field: 'factory',
    label: t('和而泰工厂'),
    slots: {
      default: ({ factory }) => {
        return <>{t(factory)}</>
      }
    }
  },
  {
    field: 'productLine',
    label: t('生产线'),
    slots: {
      default: ({ productLine }) => {
        return <>{t(productLine)}</>
      }
    }
  },
  {
    field: 'customer',
    label: t('客户'),
    slots: {
      default: ({ customer }) => {
        return <>{t(customer)}</>
      }
    }
  },
  {
    field: 'projectCode',
    label: t('项目编号')
  },
  {
    field: 'workOrderCode',
    label: t('工单号')
  },
  {
    field: 'workOrderNum',
    label: t('工单数量')
  },
  /* {
    field: 'machineCategory',
    label: t('机种'),
    slots: {
      default: ({ machineCategory }) => {
        return <>{t(machineCategory)}</>
      }
    }
  }, */
  {
    field: 'machineType',
    label: t('机型'),
    slots: {
      default: ({ machineType }) => {
        return <>{t(machineType)}</>
      }
    }
  },
  {
    field: 'productStep',
    label: t('产品阶段'),
    span: 18,
    slots: {
      default: ({ productStep }) => {
        return <>{t(productStep)}</>
      }
    }
  },
  {
    field: 'descriptions',
    label: t('问题描述'),
    span: 24,
    slots: {
      default: ({ descriptions }) => {
        return (
          <ElCollapse modelValue={['what', 'why', 'where', 'when', 'who', 'how_d', 'how_m']}>
            <ElCollapseItem title={'What happened:/发生了什么:'} name="what">
              {descriptions?.what ?? '-'}
            </ElCollapseItem>
            <ElCollapseItem title={'Why is it an issue:/为什么是一个问题:'} name="why">
              {descriptions?.why ?? '-'}
            </ElCollapseItem>
            <ElCollapseItem
              title={'Where was this issue detected:/在哪里发现这个问题:'}
              name="where"
            >
              {descriptions?.where ?? '-'}
            </ElCollapseItem>
            <ElCollapseItem title={'When detected:/什么时候发现:'} name="when">
              {descriptions?.when ?? '-'}
            </ElCollapseItem>
            <ElCollapseItem title={'Who detected the issue:/谁发现这个问题:'} name="who">
              {descriptions?.who ?? '-'}
            </ElCollapseItem>
            <ElCollapseItem title={'How was the issue detected:/怎么发现这个问题:'} name="how_d">
              {descriptions?.how_detected ?? '-'}
            </ElCollapseItem>
            <ElCollapseItem title={'How many:/发现数量:'} name="how_m">
              {descriptions?.how_many ?? '-'}
            </ElCollapseItem>
          </ElCollapse>
        )
      }
    }
  },
  {
    field: 'images',
    label: t('图片'),
    span: 24,
    slots: {
      default: ({ id, goodPartImages = [], badPartImages = [] }: Problem) => {
        const _goodPartImages = goodPartImages === null ? [] : goodPartImages
        const _badPartImages = badPartImages === null ? [] : badPartImages
        return (
          <ElRow class="w-full">
            <ElCol span={12}>
              <div class={'w-full border-1px border-solid border-transparent border-r-0'}>
                <div
                  class={
                    'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-success-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                  }
                >
                  {t('好件')}
                </div>
                <div class={'py-3px px-3px'}>
                  <div
                    class={
                      'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-success-light-5)]'
                    }
                  >
                    <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                      {_goodPartImages.map(({ key }) => {
                        const src = getPreviewURLApi(id, key, 'good')
                        return (
                          <div class={'group relative w-full h-296px flex'}>
                            <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                          </div>
                        )
                      })}
                    </ElSpace>
                  </div>
                </div>
              </div>
            </ElCol>
            <ElCol span={12}>
              <div class={'w-full border-1px border-solid border-transparent'}>
                <div
                  class={
                    'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-error-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                  }
                >
                  {t('坏件')}
                </div>
                <div class={'py-3px px-3px'}>
                  <div
                    class={
                      'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-error-light-5)]'
                    }
                  >
                    <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                      {_badPartImages.map(({ key }) => {
                        const src = getPreviewURLApi(id, key, 'bad')
                        return (
                          <div class={'group relative w-full h-296px flex'}>
                            <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                          </div>
                        )
                      })}
                    </ElSpace>
                  </div>
                </div>
              </div>
            </ElCol>
          </ElRow>
        )
      }
    }
  }
])
</script>

<template>
  <Descriptions
    direction="vertical"
    :column="3"
    :title="t('问题描述')"
    :schema="schema"
    :data="currentRow"
  />
  <Reason :id="currentRow?.id" :readonly="actionType !== 'edit'" />
  <Log :id="currentRow?.id" :is-show="actionType !== 'edit'" />
</template>
