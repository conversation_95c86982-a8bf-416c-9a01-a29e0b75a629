<script setup lang="ts">
import { Echart } from '@/components/Echart'
import { computed } from 'vue'
import dayjs from 'dayjs'
import { EChartsOption, EChartsType } from 'echarts'
import { useDashboardStore } from '@/store/modules/dashboard'
import { getBadTypeChartApi } from '@/api/dashboard'
import { SearchParams } from '@/api/dashboard/types'
import { DictionaryCategory } from '@/api/dictionary/types'
import { useI18n } from '@/hooks/web/useI18n'
const props = defineProps<{
  start: Date
  end: Date
  loading: { status: boolean }
}>()
const store = useDashboardStore()

const $emit = defineEmits(['update:loading'])

const echartRef = ref<{ getECharts: () => EChartsType } | null>(null)
const { t } = useI18n()
$emit('update:loading', { status: true })
watchEffect(async () => {
  const { data } = await getBadTypeChartApi({
    ...store.filter,
    monthRange: [props.start, props.end]
  } as SearchParams)
  setTimeout(() => {
    modifyOptions.value = data.data
  }, 100)
})

const options = ref<EChartsOption>({
  tooltip: {
    formatter: (params) => {
      let str = `${params[0].name}<br/>`
      for (let i = 0; i < params.length; i++) {
        str += `<div style='min-width:6.25rem'>${
          params[i].marker
        } <span style='margin-right:1.5625rem'>${t(
          params[i].seriesName
        )}</span><b style='position:absolute;right:0.625rem'>${params[i].value}</b></div>`
      }
      return str
    },
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  yAxis: {}
})
const modifyOptions = computed<Record<string, any>>({
  get: () => options.value,
  set: (data: { month: string; type: string; value: string }[]) => {
    const legend = store.getOptions(DictionaryCategory.UNQUALITY_TYPE)
    const itemStyles = [
      {
        color: 'rgb(0, 157, 255)'
      },
      {
        color: 'rgb(34, 228, 255)'
      },
      {
        color: 'rgb(59, 255, 208)'
      },
      {
        color: 'rgb(4, 227, 138)'
      },
      {
        color: 'rgb(157, 255, 134)'
      },
      {
        color: 'rgb(254, 229, 136)'
      }
    ]
    const startMonth = dayjs(props.start)
    const endMonth = dayjs(props.end)
    const xAxisData: string[] = []
    const monthRange: string[] = []
    const diffYear = props.start.getFullYear() !== props.end.getFullYear()
    for (let m = startMonth; !m.isAfter(endMonth); m = m.add(1, 'months')) {
      monthRange.push(m.format('YYYY-MM'))
      if (diffYear) {
        xAxisData.push(m.format('YYYY-MM'))
      } else {
        xAxisData.push(m.format('M月'))
      }
    }
    const emphasisStyle = {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0,0,0,0.3)'
      }
    }

    options.value.legend = {
      data: legend,
      type: 'scroll',
      left: '10%',
      formatter: (name) => t(name)
    }
    /* options.value.dataZoom = [
      {
        height: 18,
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        start: (100 / xAxisData.length) * Math.max(xAxisData.length - 12, 0),
        end: 100
      }
    ] */
    options.value.xAxis = {
      data: xAxisData,
      axisLine: { onZero: true },
      splitLine: { show: false },
      splitArea: { show: false },
      triggerEvent: true,
      axisLabel: diffYear ? { rotate: 45 } : { rotate: 0 }
    }
    options.value.grid = {
      bottom: diffYear ? 48 : 30
    }
    options.value.series = legend.map((name, index) => {
      return {
        name,
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: emphasisStyle,
        data: monthRange.map((month) => {
          return data.find((datum) => datum.month === month && datum.type === name)?.value ?? 0
        }),
        itemStyle: itemStyles[index]
      }
    })
    const _echartRef = unref(echartRef)
    if (!!_echartRef) {
      const echarts = _echartRef.getECharts()
      echarts.setOption(options.value, true)
    }
  }
})
watch(
  () => echartRef.value,
  (echartRef) => {
    if (echartRef) {
      const echarts = echartRef.getECharts()
      echarts.getZr().on('click', function (params) {
        const pointInPixel = [params.offsetX, params.offsetY] // 转换像素坐标值到逻辑坐标系上的点
        if (echarts.containPixel('grid', pointInPixel)) {
          const pointInGrid = echarts.convertFromPixel({ seriesIndex: 0 }, pointInPixel)
          const op = echarts.getOption() as EChartsOption
          const xIndex = pointInGrid[0]
          let min = 0
          const val = pointInGrid[1]
          for (const series of (op.series as any[]).sort(
            (a, b) => a.data[xIndex] - b.data[xIndex]
          )) {
            const max = min + series.data[xIndex]
            if (val >= min && val <= max) {
              if (series.data[xIndex] === 0) {
                return
              }
              store.seriesFilter = { unqualityType: [series.name] }
              const name = op.xAxis![0].data[xIndex]
              if (name.length > 4) {
                store.monthRange = [
                  dayjs(name, 'YYYY-MM').startOf('M').toDate(),
                  dayjs(name, 'YYYY-MM').endOf('M').toDate()
                ]
              } else {
                const year = dayjs(props.start).year()
                store.monthRange = [
                  dayjs(`${year}-${name}`, 'YYYY-M月').startOf('M').toDate(),
                  dayjs(`${year}-${name}`, 'YYYY-M月').endOf('M').toDate()
                ]
              }
              store.setTableDialogVisible(true)
            }
            min = max
          }
        }
      })
    }
  },
  { deep: true }
)
</script>
<template>
  <Echart
    ref="echartRef"
    v-if="options.legend"
    :options="options as EChartsOption"
    height="18.75rem"
  />
</template>
