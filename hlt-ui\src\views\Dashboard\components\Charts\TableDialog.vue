<script setup lang="tsx">
import {
  ElButton,
  ElCol,
  ElDialog,
  ElIcon,
  ElImage,
  ElRow,
  ElSpace,
  ElTable,
  ElTableColumn,
  ElTag
} from 'element-plus'
import { Drawer } from '@/components/Drawer'
import { useDashboardStore } from '@/store/modules/dashboard'
import { getChartDataApi } from '@/api/dashboard'
import { SearchParams } from '@/api/dashboard/types'
import Exporter from '@/utils/exporterUtil'
import dayjs from 'dayjs'
import { useI18n } from '@/hooks/web/useI18n'
import { BaseButton } from '@/components/Button'
import { getPreviewURLApi } from '@/api/problem'
import { ComponentNameEnum } from '@/components/Form'
import Write from '@/views/Problem/components/write/Index.vue'
import { Problem, ProblemStatusRecords, ReasonStateOptions } from '@/api/problem/types'
import { useLocaleStore } from '@/store/modules/locale'
const localeStore = useLocaleStore()
const store = useDashboardStore()
const { t } = useI18n()
const data = ref<Problem[]>([])
const drawerVisible = ref(false)
const currentRow = ref<Partial<Problem> | null>(null)
const actionType = ref('')
const action = (row: Partial<Problem> | null, type: string) => {
  actionType.value = type
  currentRow.value = row
  drawerVisible.value = true
}
const formSchemas = ref([
  {
    field: 'code',
    label: t('问题编号'),
    component: ComponentNameEnum.TEXT,
    colProps: { xl: 3, lg: 3, md: 3, span: 12 }
  },
  {
    field: 'createdOn',
    label: t('创建日期'),
    component: ComponentNameEnum.TEXT,
    colProps: { xl: 3, lg: 3, md: 3, span: 12 }
  },
  {
    field: 'businessUnit',
    label: t('事业部'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'factory',
    label: t('和而泰工厂'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'productLine',
    label: t('生产线'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'customer',
    label: t('客户'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'projectCode',
    label: t('项目编号'),
    component: ComponentNameEnum.TEXT,
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'workOrderCode',
    label: t('工单号'),
    component: ComponentNameEnum.TEXT,
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'workOrderNum',
    label: t('工单数量'),
    component: ComponentNameEnum.TEXT,
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'machineType',
    label: t('机型'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'productStep',
    label: t('产品阶段'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { xl: 6, lg: 6, md: 6, span: 12 }
  },
  {
    field: 'descriptionDivider',
    label: t('问题描述'),
    component: ComponentNameEnum.DIVIDER,
    colProps: { span: 24 }
  },
  {
    field: 'descriptions.what',
    label: localeStore.isEn ? 'What happened:' : 'What happened:\n发生了什么:',
    colProps: { span: 8 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.descriptions?.what}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'descriptions.why',
    label: localeStore.isEn ? 'Why is it an issue:' : 'Why is it an issue:\n为什么是一个问题:',
    colProps: { span: 8 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.descriptions?.why}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'descriptions.where',
    label: localeStore.isEn
      ? 'Where was the issue detected:'
      : 'Where was the issue detected:\n在哪里发现这个问题:',
    colProps: { span: 8 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.descriptions?.where}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'descriptions.when',
    label: localeStore.isEn ? 'When detected:' : 'When detected:\n什么时候发现:',
    colProps: { span: 8 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.descriptions?.when}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'descriptions.who',
    label: localeStore.isEn
      ? 'Who detected the issue:'
      : 'Who detected the issue:\n谁发现这个问题:',
    colProps: { span: 8 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.descriptions?.who}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'descriptions.how_detected',
    label: localeStore.isEn
      ? 'How was the issue detected:'
      : 'How was the issue detected:\n怎么发现的这个问题:',
    colProps: { span: 8 },
    formItemProps: {
      slots: {
        default: ({ formValue }: { formValue: Problem }) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.descriptions?.how_detected}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'descriptions.how_many',
    label: localeStore.isEn ? 'How many:' : 'How many:\n发现数量:',
    colProps: { span: 8 },
    formItemProps: {
      slots: {
        default: ({ formValue }: { formValue: Problem }) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.descriptions?.how_many}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'images',
    label: t('图片'),
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          const { id, goodPartImages = [], badPartImages = [] } = formValue as Problem
          const _goodPartImages = goodPartImages === null ? [] : goodPartImages
          const _badPartImages = badPartImages === null ? [] : badPartImages
          return (
            <ElRow class="w-full">
              <ElCol span={12}>
                <div class={'w-full border-1px border-solid border-transparent border-r-0'}>
                  <div
                    class={
                      'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-success-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                    }
                  >
                    {t('好件')}
                  </div>
                  <div class={'py-3px px-3px'}>
                    <div
                      class={
                        'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-success-light-5)]'
                      }
                    >
                      <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                        {_goodPartImages.map(({ key }) => {
                          const src = getPreviewURLApi(id, key, 'good')
                          return (
                            <div class={'group relative w-full h-296px flex'}>
                              <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                            </div>
                          )
                        })}
                      </ElSpace>
                    </div>
                  </div>
                </div>
              </ElCol>
              <ElCol span={12}>
                <div class={'w-full border-1px border-solid border-transparent'}>
                  <div
                    class={
                      'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-error-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                    }
                  >
                    {t('坏件')}
                  </div>
                  <div class={'py-3px px-3px'}>
                    <div
                      class={
                        'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-error-light-5)]'
                      }
                    >
                      <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                        {_badPartImages.map(({ key }) => {
                          const src = getPreviewURLApi(id, key, 'bad')
                          return (
                            <div class={'group relative w-full h-296px flex'}>
                              <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                            </div>
                          )
                        })}
                      </ElSpace>
                    </div>
                  </div>
                </div>
              </ElCol>
            </ElRow>
          )
        }
      }
    }
  }
])
watchEffect(async () => {
  if (store.isTableDialogVisible) {
    const result = await getChartDataApi({
      ...store.filter,
      monthRange: store.monthRange,
      ...store.seriesFilter
    } as SearchParams)
    data.value = result.data
  } else {
    store.seriesFilter = undefined
    store.monthRange = undefined
    data.value = []
  }
})
const exportData = async () => {
  const exporter = new Exporter()
  const sheet = await exporter.addSheet({
    header: [
      { header: t('问题编号'), key: 'code' },
      { header: t('创建日期'), key: 'createdOn' },
      { header: t('机型'), key: 'machineType' },
      { header: t('客户'), key: 'customer' },
      { header: t('项目编号'), key: 'projectCode' },
      { header: t('工单号'), key: 'workOrderCode' },
      { header: t('工单数量'), key: 'workOrderNum' },
      { header: t('和而泰工厂'), key: 'factory' },
      { header: t('事业部'), key: 'businessUnit' },
      { header: t('创建人'), key: 'creatorName' },
      { header: t('产品阶段'), key: 'productStep' },
      { header: t('问题描述'), key: 'descriptions' },
      { header: t('状态'), key: 'status' },
      { header: t('当前节点'), key: 'node' },
      { header: t('责任人'), key: 'responsor' },
      { header: t('下一节点'), key: 'nextNode' }
    ],
    data: data.value.map(
      ({
        code,
        createdOn,
        machineType,
        customer,
        projectCode,
        workOrderCode,
        workOrderNum,
        factory,
        businessUnit,
        creatorName,
        productStep,
        descriptions,
        status,
        reasons
      }) => ({
        code,
        createdOn,
        machineType: t(machineType),
        customer: t(customer),
        projectCode,
        workOrderCode,
        workOrderNum,
        factory: t(factory),
        businessUnit: t(businessUnit),
        creatorName,
        productStep,
        descriptions: localeStore.isEn
          ? `1.What happened:${descriptions?.what ?? ''}\n2.Why is it an issue:${
              descriptions?.why ?? ''
            }\n3.Where was the issue detected:${descriptions?.where ?? ''}\n4.When detected:${
              descriptions?.when ?? ''
            };\n5.Who detected the issue:${descriptions?.who ?? ''}\n6.How was the issue detected:${
              descriptions?.how_detected ?? ''
            }\n7.How many:${descriptions?.how_many ?? ''}`
          : `1.发生了什么:${descriptions?.what ?? ''}\n2.为什么是一个问题:${
              descriptions?.why ?? ''
            }\n3.在哪里发现这个问题:${descriptions?.where ?? ''}\n4.什么时候发现:${
              descriptions?.when ?? ''
            };\n5.谁发现这个问题:${descriptions?.who ?? ''}\n6.怎么发现的这个问题:${
              descriptions?.how_detected ?? ''
            }\n7.发现数量:${descriptions?.how_many ?? ''}`,
        status: t(ProblemStatusRecords[status]),
        node: reasons.map((reason) => t(ReasonStateOptions[reason.state])).join('\n'),
        responsor: reasons
          .map(
            (reason) =>
              reason.configs.find((config) => config.state === reason.state)?.ownerName ?? '-'
          )
          .join('\n'),
        nextNode: reasons
          .map((reason) => {
            const current = reason.configs.find((config) => config.state === reason.state)
            const next = reason.configs.find(
              (config) => config.stateIdx === (current?.stateIdx ?? -999) + 1
            )
            return `${!!next && !!next.state ? t(ReasonStateOptions[next.state]) : '-'}${
              !!next && !!next.ownerName?.length ? '[' + next.ownerName + ']' : ''
            }`
          })
          .join('\n')
      })
    )
  })
  sheet.columns.forEach((column) => {
    if (
      column.key === 'descriptions' ||
      column.key === 'node' ||
      column.key === 'responsor' ||
      column.key === 'nextNode'
    ) {
      column.eachCell?.((cell, idx) => {
        if (idx > 1) {
          cell.style = {
            ...cell.style,
            alignment: {
              ...cell.style?.alignment,
              horizontal: column.key === 'descriptions' ? 'left' : 'center',
              wrapText: true
            }
          }
        }
      })
    }
  })
  exporter.doExport('问题_' + dayjs().format('YYYYMMDDHHmmss') + '.xlsx')
}

const formatter = (row, _, cellValue) => {
  return (
    <BaseButton type="info" link onClick={() => action(row, 'view')}>
      {cellValue}
    </BaseButton>
  )
}
const nodeFormatter = (entity: Problem) => {
  if ((entity.reasons ?? []).length === 1) {
    return <ElTag>{t(ReasonStateOptions[entity.reasons[0].state])}</ElTag>
  }
  return (
    <ElSpace direction="vertical">
      {(entity.reasons ?? []).map((reason) => {
        return <ElTag>{t(ReasonStateOptions[reason.state])}</ElTag>
      })}
    </ElSpace>
  )
}
const statusFormatter = (_, __, cellValue: string) => {
  const label = ProblemStatusRecords[cellValue]
  if (!!label) {
    return t(label)
  }
  return '-'
}
const ownerFormatter = (entity: Problem) => {
  if ((entity.reasons ?? []).length === 1) {
    const reason = entity.reasons[0]
    return (
      <ElTag>
        {reason.configs.find((config) => config.state === reason.state)?.ownerName ?? '-'}
      </ElTag>
    )
  }

  return (
    <ElSpace direction="vertical">
      {(entity.reasons ?? []).map((reason) => {
        return (
          <ElTag>
            {reason.configs.find((config) => config.state === reason.state)?.ownerName ?? '-'}
          </ElTag>
        )
      })}
    </ElSpace>
  )
}
const nextNodeFormatter = (entity: Problem) => {
  if ((entity.reasons ?? []).length === 1) {
    const current = entity.reasons[0].configs.find(
      (config) => config.state === entity.reasons[0].state
    )
    const next = entity.reasons[0].configs.find(
      (config) => config.stateIdx === (current?.stateIdx ?? -999) + 1
    )
    return (
      <ElTag>
        {!!next && !!next.state ? t(ReasonStateOptions[next.state]) : '-'}
        {!!next && !!next.ownerName?.length ? '[' + next.ownerName + ']' : ''}
      </ElTag>
    )
  }
  return (
    <ElSpace direction="vertical">
      {(entity.reasons ?? []).map((reason) => {
        const current = reason.configs.find((config) => config.state === reason.state)
        const next = reason.configs.find(
          (config) => config.stateIdx === (current?.stateIdx ?? -999) + 1
        )
        return (
          <ElTag>
            {!!next && !!next.state ? t(ReasonStateOptions[next.state]) : '-'}
            {!!next && !!next.ownerName?.length ? '[' + next.ownerName + ']' : ''}
          </ElTag>
        )
      })}
    </ElSpace>
  )
}
const remarkFormatter = (entity: Problem) => {
  if ((entity.reasons ?? []).length === 1) {
    const reason = entity.reasons[0]
    return <ElTag>{reason.remark ?? '-'}</ElTag>
  }
  return (
    <ElSpace direction="vertical">
      {(entity.reasons ?? []).map((reason) => {
        return <ElTag>{reason.remark ?? '-'}</ElTag>
      })}
    </ElSpace>
  )
}
</script>

<template>
  <ElDialog
    draggable
    v-model="store.tableDialog.visible"
    width="75%"
    top="5vh"
    @update:model-value="
      (visible) => {
        store.setTableDialogVisible(visible)
      }
    "
  >
    <!--    <el-table-column property="date" label="Date" width="150" />
    <el-table-column property="name" label="Name" width="200" />
    <el-table-column property="address" label="Address" />
-->
    <template #header>
      <div style="display: flex; padding-left: 1rem; height: 100%; align-items: center">
        <ElButton type="primary" :disabled="!data.length" @click="exportData">
          <ElSpace>
            <ElIcon>
              <svg
                stroke="currentColor"
                fill="currentColor"
                stroke-width="0"
                viewBox="0 0 24 24"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M2.85858 2.87756L15.4293 1.08175C15.7027 1.0427 15.9559 1.23265 15.995 1.50601C15.9983 1.52943 16 1.55306 16 1.57672V22.4237C16 22.6999 15.7761 22.9237 15.5 22.9237C15.4763 22.9237 15.4527 22.922 15.4293 22.9187L2.85858 21.1229C2.36593 21.0525 2 20.6306 2 20.1329V3.86751C2 3.36986 2.36593 2.94794 2.85858 2.87756ZM17 3.00022H21C21.5523 3.00022 22 3.44793 22 4.00022V20.0002C22 20.5525 21.5523 21.0002 21 21.0002H17V3.00022ZM10.2 12.0002L13 8.00022H10.6L9 10.2859L7.39999 8.00022H5L7.8 12.0002L5 16.0002H7.39999L9 13.7145L10.6 16.0002H13L10.2 12.0002Z"
                />
              </svg>
            </ElIcon>
            {{ t('导出Excel') }}
          </ElSpace>
        </ElButton>
      </div>
    </template>
    <div class="min-h-[300px] max-h-[calc(100vh-85px)] overflow-y-auto overflow-x-auto">
      <ElTable :data="data">
        <ElTableColumn :width="140" property="code" :label="t('问题编号')" :formatter="formatter" />
        <ElTableColumn :width="120" property="createdOn" :label="t('创建日期')" />
        <ElTableColumn
          property="machineType"
          :width="120"
          :label="t('机型')"
          :formatter="(_, __, value) => t(value)"
        />
        <ElTableColumn
          property="customer"
          :width="120"
          :label="t('客户')"
          :formatter="(_, __, value) => t(value)"
        />
        <ElTableColumn :width="120" property="projectCode" :label="t('项目编号')" />
        <ElTableColumn property="workOrderCode" :label="t('工单号')" />
        <ElTableColumn :width="140" property="workOrderNum" :label="t('工单数量')" />
        <ElTableColumn :width="120" property="factory" :label="t('和而泰工厂')" />
        <ElTableColumn
          property="businessUnit"
          :width="120"
          :label="t('事业部')"
          :formatter="(_, __, value) => t(value)"
        />
        <ElTableColumn property="creatorName" :width="120" :label="t('创建人')" />
        <ElTableColumn property="descriptions.what" :width="160" :label="t('问题描述')" />
        <ElTableColumn
          property="productStep"
          :label="t('阶段')"
          :formatter="(_, __, value) => t(value)"
        />
        <ElTableColumn property="status" :label="t('状态')" :formatter="statusFormatter" />
        <ElTableColumn
          :min-width="150"
          property="node"
          :label="t('当前节点')"
          :formatter="nodeFormatter"
        />
        <ElTableColumn
          :min-width="150"
          property="owner"
          :label="t('责任人')"
          :formatter="ownerFormatter"
        />
        <ElTableColumn
          :min-width="250"
          property="nextNode"
          :label="t('下一节点')"
          :formatter="nextNodeFormatter"
        />
        <ElTableColumn
          :min-width="120"
          property="remark"
          :label="t('备注')"
          :formatter="remarkFormatter"
        />
      </ElTable>
    </div>
  </ElDialog>

  <Drawer v-model="drawerVisible" :fullscreen="false" :title="t('查看问题')" default-fullscreen>
    <Write
      ref="writeRef"
      :action-type="actionType"
      :draft="false"
      :form-schema="formSchemas"
      :current-row="currentRow"
    />
    <template #footer>
      <BaseButton @click="drawerVisible = false">{{ t('关闭') }}</BaseButton>
    </template>
  </Drawer>
</template>
