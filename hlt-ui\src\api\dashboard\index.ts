import { SearchParams } from '@/api/dashboard/types'
import request from '@/axios'
import { PanelCountType } from './types'
import { PageRequest } from './../../utils/index'
const BASE_API = '/dashboard'

export const getPanelCountApi = (): Promise<IResponse<PanelCountType>> => {
  return request.get({ url: `${BASE_API}/panelCount` })
}

export const getProcessingCountApi = (): Promise<IResponse<number>> => {
  return request.get({ url: `${BASE_API}/processing` })
}

export const getStepChartApi = (query: SearchParams, pie: boolean = false) => {
  return request.post({
    url: `${BASE_API}/chart/step/${pie ? 1 : 0}`,
    data: new PageRequest({ query })
  })
}
export const getCustomerChartApi = (query: SearchParams, pie: boolean = false) => {
  return request.post({
    url: `${BASE_API}/chart/customer/${pie ? 1 : 0}`,
    data: new PageRequest({ query })
  })
}
export const getBadTypeChartApi = (query: SearchParams, pie: boolean = false) => {
  return request.post({
    url: `${BASE_API}/chart/unqualityType/${pie ? 1 : 0}`,
    data: new PageRequest({ query })
  })
}
export const getReasonChartApi = (query: SearchParams, category?: string) => {
  return request.post({
    url: !!category?.length
      ? `${BASE_API}/chart/category/${category}`
      : `${BASE_API}/chart/category`,
    data: new PageRequest({ query })
  })
}

export const getChartDataApi = (query: SearchParams) => {
  return request.post({
    url: `${BASE_API}/chart/data`,
    data: new PageRequest({ query })
  })
}

export const getPanelDataApi = (panel: 'NEW' | 'PROCESSING' | 'PROTECT_PROCESSING') => {
  if (panel === 'PROTECT_PROCESSING') {
    return request.get({
      url: `${BASE_API}/processing/data`
    })
  } else {
    return request.get({
      url: `${BASE_API}/panel/data/${panel}`
    })
  }
}

export const getTodoApi = () => {
  return request.get({
    url: `${BASE_API}/todo`
  })
}
