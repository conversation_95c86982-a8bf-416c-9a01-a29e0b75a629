<script setup lang="ts">
import { getPanelCountApi, getProcessingCountApi } from '@/api/dashboard'
import type { PanelCountType } from '@/api/dashboard/types'
import { CountTo } from '@/components/CountTo'
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { useDashboardStore } from '@/store/modules/dashboard'
import { useUserStore } from '@/store/modules/user'
import { isDef } from '@/utils/is'
import { ElCard, ElCol, ElRow, ElSkeleton } from 'element-plus'
import { reactive, ref } from 'vue'

const { t } = useI18n()

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('panel')

const loading = ref(true)

let totalState = reactive<PanelCountType>({
  open: 0,
  follow: 0,
  processing: 0
})

const userStore = useUserStore()
const store = useDashboardStore()
const isLogin = isDef(userStore.getUserInfo)
const getCount = async () => {
  const res = await getPanelCountApi()
  totalState = Object.assign(totalState, res?.data || {})
  if (isLogin) {
    const { data } = await getProcessingCountApi()
    totalState.processing = data
  }
  loading.value = false
}

getCount()
</script>

<template>
  <ElRow :gutter="20" justify="space-between" :class="prefixCls">
    <ElCol :xl="8" :span="8">
      <ElCard
        shadow="hover"
        class="mb-20px"
        @click="store.setPanelDialogVisible(true, 'NEW')"
        :body-style="{
          cursor: 'pointer',
          backgroundImage: 'linear-gradient(108.042deg, #0370ff 2.72633e-15%, #63c9ff 100%)'
        }"
      >
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div :class="`${prefixCls}__item flex justify-between`">
              <div class="flex flex-col justify-between">
                <div :class="`${prefixCls}__item--text text-24px text-white text-left font-700`">{{
                  t('待跟进')
                }}</div>
                <CountTo
                  class="text-24px text-white font-700 text-left"
                  :start-val="0"
                  :end-val="totalState.open"
                  :duration="2000"
                />
              </div>
              <div :class="`${prefixCls}__item--icon`">
                <Icon icon="uil:database" :size="64" />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>

    <ElCol :xl="8" :span="8">
      <ElCard
        shadow="hover"
        class="mb-20px"
        @click="store.setPanelDialogVisible(true, 'PROCESSING')"
        :body-style="{
          cursor: 'pointer',
          backgroundImage: 'linear-gradient(108.042deg, #f46464 2.72633e-15%, #fe9797 100%)'
        }"
      >
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div :class="`${prefixCls}__item flex justify-between`">
              <div class="flex flex-col justify-between">
                <div :class="`${prefixCls}__item--text text-24px text-white text-left font-700`">{{
                  t('跟进中')
                }}</div>
                <CountTo
                  class="text-24px text-white font-700 text-left"
                  :start-val="0"
                  :end-val="totalState.follow"
                  :duration="2000"
                />
              </div>
              <div :class="`${prefixCls}__item--icon`">
                <Icon icon="gg:list" :size="64" />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>

    <ElCol :xl="8" :span="8">
      <ElCard
        shadow="hover"
        class="mb-20px"
        @click="store.setPanelDialogVisible(true, 'PROTECT_PROCESSING')"
        :body-style="{
          cursor: 'pointer',
          backgroundImage: 'linear-gradient(108.042deg, #ff9239 2.72633e-15%, #ffcb97 100%)'
        }"
      >
        <ElSkeleton :loading="loading" animated :rows="2">
          <template #default>
            <div :class="`${prefixCls}__item flex justify-between`">
              <div class="flex flex-col justify-between">
                <div :class="`${prefixCls}__item--text text-24px text-white text-left font-700`">{{
                  t('待处理')
                }}</div>
                <CountTo
                  v-if="isLogin"
                  class="text-24px text-white font-700 text-left"
                  :start-val="0"
                  :end-val="totalState.processing"
                  :duration="2000"
                />
                <span v-else class="text-24px text-white font-700 text-left">N/A</span>
              </div>
              <div :class="`${prefixCls}__item--icon`">
                <Icon icon="icons8:todo-list" :size="64" />
              </div>
            </div>
          </template>
        </ElSkeleton>
      </ElCard>
    </ElCol>
  </ElRow>
</template>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-panel';

.@{prefix-cls} {
  &__item {
    &:hover {
      .@{prefix-cls}__item--icon {
        opacity: 1;
      }
    }

    .@{prefix-cls}__item--icon {
      color: #fff;
      opacity: 0.4;
      transition: opacity 0.38s ease-out;
    }
  }
}
</style>
