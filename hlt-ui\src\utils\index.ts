import { BaseEntity } from '@/api/base/types'
import Fingerprint2 from 'fingerprintjs2'
import { cloneDeep, get, isNil, omitBy } from 'lodash-es'
import { isDef, isEmpty, isObject } from './is'

export const createFingerprint = async () => {
  return new Promise<string>((resolve) => {
    Fingerprint2.get((components) => {
      const values = components.map((component) =>
        component.key === 'deviceMemory' ? 'not available' : component.value
      )
      resolve(Fingerprint2.x64hash128(values.join(''), 31))
    })
  })
}

/**
 *
 * @param component 需要注册的组件
 * @param alias 组件别名
 * @returns any
 */
export const withInstall = <T>(component: T, alias?: string) => {
  const comp = component as any
  comp.install = (app: any) => {
    app.component(comp.name || comp.displayName, component)
    if (alias) {
      app.config.globalProperties[alias] = component
    }
  }
  return component as T & Plugin
}

/**
 * @param str 需要转下划线的驼峰字符串
 * @returns 字符串下划线
 */
export const humpToUnderline = (str: string): string => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

/**
 * @param str 需要转驼峰的下划线字符串
 * @returns 字符串驼峰
 */
export const underlineToHump = (str: string): string => {
  if (!str) return ''
  return str.replace(/\-(\w)/g, (_, letter: string) => {
    return letter.toUpperCase()
  })
}

/**
 * 驼峰转横杠
 */
export const humpToDash = (str: string): string => {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}

export const setCssVar = (prop: string, val: any, dom = document.documentElement) => {
  dom.style.setProperty(prop, val)
}

/**
 * 查找数组对象的某个下标
 * @param {Array} ary 查找的数组
 * @param {Functon} fn 判断的方法
 */
// eslint-disable-next-line
export const findIndex = <T = Recordable>(ary: Array<T>, fn: Fn): number => {
  if (ary.findIndex) {
    return ary.findIndex(fn)
  }
  let index = -1
  ary.some((item: T, i: number, ary: Array<T>) => {
    const ret: T = fn(item, i, ary)
    if (ret) {
      index = i
      return ret
    }
  })
  return index
}

export const trim = (str: string) => {
  return str.replace(/(^\s*)|(\s*$)/g, '')
}

/**
 * @param {Date | number | string} time 需要转换的时间
 * @param {String} fmt 需要转换的格式 如 yyyy-MM-dd、yyyy-MM-dd HH:mm:ss
 */
export function formatTime(time: Date | number | string, fmt: string) {
  if (!time) return ''
  else {
    const date = new Date(time)
    const o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'H+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      S: date.getMilliseconds()
    }
    if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (const k in o) {
      if (new RegExp('(' + k + ')').test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
        )
      }
    }
    return fmt
  }
}

/**
 * 生成随机字符串
 */
export function toAnyString() {
  const str: string = 'xxxxx-xxxxx-4xxxx-yxxxx-xxxxx'.replace(/[xy]/g, (c: string) => {
    const r: number = (Math.random() * 16) | 0
    const v: number = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString()
  })
  return str
}

/**
 * 首字母大写
 */
export function firstUpperCase(str: string) {
  return str.toLowerCase().replace(/( |^)[a-z]/g, (L) => L.toUpperCase())
}

function flattenQuery<T extends Recordable>(query: T) {
  const _query = {}
  for (const key of Object.keys(query)) {
    const value = query[key]
    if (Array.isArray(value) || typeof value !== 'object' || value.hasOwnProperty('operate')) {
      _query[key] = value
    } else if (typeof value === 'object') {
      const nestedQuery = flattenQuery(value)
      for (const _key of Object.keys(nestedQuery)) {
        _query[`${key}.${_key}`] = nestedQuery[_key]
      }
    }
  }
  return _query as unknown as T
}

export function query<T extends BaseEntity>(data: T[], body: Recordable = {}) {
  const { operations = {}, page = {}, params = {} } = body
  let _data: T[] = data
  if (!!Object.keys(params).length) {
    const keys = Object.keys(params)
    _data = _data.filter((item) => {
      return keys.every((key) => {
        const targetValue = params[key]
        const operate = operations[key]
        const sourceValue = get(item, key)
        if (operate === Operators.IN) {
          if (Array.isArray(targetValue)) {
            return !targetValue.length || targetValue.includes(sourceValue)
          } else {
            return targetValue === sourceValue
          }
        } else if (operate === Operators.LIKE) {
          return (
            sourceValue.toString().toLowerCase().indexOf(targetValue.toString().toLowerCase()) > -1
          )
        } else {
          return targetValue === sourceValue
        }
      })
    })
  }
  const { no, orders = [], size } = page
  if (!isEmpty(orders)) {
    const [sort] = orders as any[]
    const { property = 'id', direction = 'ASC' } = sort
    _data.sort((o1, o2) => {
      const sortOrder = direction === 'ASC' ? 1 : -1
      const v1 = get(o1, property)
      const v2 = get(o2, property)
      if (v1 === undefined || v1 === null) {
        return 1
      }
      if (v2 === undefined || v2 === null) {
        return -1
      }
      if (property === 'id') {
        return (o1.id as number) - (o2.id as number)
      }
      return (
        String(v1).localeCompare(String(v2)) * sortOrder || (o1.id as number) - (o2.id as number)
      )
    })
  }
  if (isDef(no) && isDef(size)) {
    const start = no * size
    const end = Math.min(start + size, _data.length)
    return {
      data: _data.length > 0 ? _data.slice(start, end) : [],
      total: _data.length,
      pageNo: no,
      pageSize: size
    }
  } else {
    return _data
  }
}

export function genId<T extends BaseEntity>(data: T[]) {
  return Math.max(...data.map((item) => item.id as number)) + 1
}

export function flatten<T extends Record<string, any>, R>(
  input: T[] | T,
  options: {
    extractor?: (input: T, parent?: T) => R
    filter?: (input: T, parent?: T) => boolean
    childrenKey?: string
    parent?: T
  } = { extractor: (t) => t as unknown as R, filter: (_) => !!_ }
): R[] {
  if (!input) return []
  const out: any[] = []
  const { extractor = (t) => t, filter = (_) => !!_, childrenKey = 'children' } = options
  if (Array.isArray(input)) {
    return input.reduce(
      (previousValue, currentValue) => previousValue.concat(flatten(currentValue, options)),
      out as any[]
    )
  }
  const node = extractor(input, options.parent)
  if (filter(input, options.parent)) {
    out.push(node)
  }
  return input[childrenKey]
    ? out.concat(flatten(input[childrenKey], { ...options, parent: input }))
    : out
}

export enum Operators {
  NOT_BETWEEN = 'not_between',
  BETWEEN = 'between',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  IN = 'in',
  NOT_IN = 'nin',
  END_WITH = 'end_with',
  NOT_END_WITH = 'not_end_with',
  START_WITH = 'start_with',
  NOT_START_WITH = 'not_start_with',
  LIKE = 'like',
  NOT_LIKE = 'not_like'
}

export type ParamRecordValue = { operate: Operators; value: any }
export type ParamRecord = Record<string, any>

export type PageRequestParams = {
  query?: ParamRecord
  page?: {
    pageNo?: number
    pageSize?: number
  }
  sort?: { prop: string; order: 'ascending' | 'descending' | null }
}

export type Order = {
  property: string
  direction: 'ASC' | 'DESC'
}
export type Page = {
  no?: number
  size?: number
  orders?: Order[]
}

export class PageRequest {
  params?: Record<string, any>
  page?: Page
  sort?: { prop: string; order: 'ascending' | 'descending' | null }
  operations?: Record<string, string>

  constructor({ page, query, sort }: PageRequestParams) {
    if (isDef(page) || isDef(sort)) {
      this.page = {}
      if (isDef(page)) {
        const { pageNo, pageSize } = page
        this.page.no = typeof pageNo === 'undefined' ? undefined : pageNo - 1
        this.page.size = pageSize
      }
      if (isDef(sort) && sort.order !== null) {
        const { order, prop } = sort
        this.page.orders = [
          {
            property: prop,
            direction: order === 'ascending' ? 'ASC' : 'DESC'
          }
        ]
      }
    }
    if (isDef(query)) {
      const _query = flattenQuery(query)
      const params = {}
      const operations = {}
      for (const key of Object.keys(_query)) {
        const val = _query[key]
        if (isDef(val) && !isEmpty(val)) {
          if (Array.isArray(val)) {
            params[key] = val
            operations[key] = Operators.IN.toUpperCase()
          } else if (isObject(val)) {
            const { value, operate } = val as ParamRecordValue
            params[key] = value
            operations[key] = operate.toUpperCase()
          } else {
            params[key] = [val]
            operations[key] = Operators.IN
          }
        }
      }
      if (!isEmpty(params)) {
        this.params = params
        this.operations = operations
      }
    }
  }
}

export function omitNil<T extends object>(datum: T) {
  return omitBy<T>(cloneDeep<T>(datum), isNil)
}
