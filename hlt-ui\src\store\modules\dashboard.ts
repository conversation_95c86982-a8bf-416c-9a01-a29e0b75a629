import { defineStore } from 'pinia'
import { store } from '../index'
import { SearchParams } from '@/api/dashboard/types'

interface DashboardState {
  monthRange?: [Date, Date]
  seriesFilter?: Record<string, string[]>
  filter?: SearchParams
  datepicker: boolean
  tableDialog: {
    visible: boolean
  }
  panelDialog: {
    visible: boolean
    panel: 'NEW' | 'PROCESSING' | 'PROTECT_PROCESSING'
  }
  options: Record<string, string[]>
}

export const useDashboardStore = defineStore('dashboard', {
  state: (): DashboardState => {
    return {
      filter: undefined,
      tableDialog: {
        visible: false
      },
      panelDialog: {
        visible: false,
        panel: 'NEW'
      },
      datepicker: true,
      options: {}
    }
  },
  getters: {
    getFilter(): SearchParams | undefined {
      return this.filter
    },
    isTableDialogVisible(): boolean {
      return this.tableDialog.visible
    },
    isPanelDialogVisible(): boolean {
      return this.panelDialog.visible
    },
    getPanel(): 'NEW' | 'PROCESSING' | 'PROTECT_PROCESSING' {
      return this.panelDialog.panel
    }
  },
  actions: {
    setFilter(filter: SearchParams) {
      this.filter = filter
    },
    getOptions(type: string) {
      return this.options[type] ?? []
    },
    setOptions(type: string | Record<string, string[]>, options?: string[]) {
      if (typeof type === 'string') {
        this.options = {
          ...this.options,
          [type]: options as string[]
        }
      } else {
        this.options = type ?? {}
      }
    },
    setTableDialogVisible(visible: boolean) {
      this.tableDialog = {
        ...this.tableDialog,
        visible: visible
      }
    },
    setPanelDialogVisible(
      visible: boolean,
      panel: 'NEW' | 'PROCESSING' | 'PROTECT_PROCESSING' = 'NEW'
    ) {
      this.panelDialog = {
        ...this.panelDialog,
        visible,
        panel
      }
    }
  },
  persist: false
})

export const useDashboardStoreWithOut = () => {
  return useDashboardStore(store)
}
