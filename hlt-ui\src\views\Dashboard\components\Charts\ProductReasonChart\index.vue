<script setup lang="ts">
import {
  ElCard,
  ElDatePicker,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElIcon,
  ElSkeleton,
  ElText
} from 'element-plus'
import dayjs from 'dayjs'
import Radar from './Radar.vue'
import { useDashboardStore } from '@/store/modules/dashboard'
import { useI18n } from '@/hooks/web/useI18n'

const { t } = useI18n()
const chartTypeRef = ref<{ type: string }>({ type: 'radar' })
const months = ref<[Date, Date]>([dayjs().add(-11, 'M').toDate(), new Date()])
const store = useDashboardStore()
const lastNMonths = (lastN: number) => {
  return [
    dayjs()
      .add(-1 * (lastN - 1), 'M')
      .toDate(),
    new Date()
  ]
}
const shortcuts: { text: string; value: any | Function }[] = [
  {
    text: t('本月'),
    value: [new Date(), new Date()]
  },
  {
    text: t('今年'),
    value: () => {
      const end = new Date()
      const start = new Date(new Date().getFullYear(), 0)
      return [start, end]
    }
  },
  {
    text: t('过去12个月'),
    value: () => lastNMonths(12)
  },
  {
    text: t('过去6个月'),
    value: () => lastNMonths(6)
  },
  {
    text: t('过去3个月'),
    value: () => lastNMonths(3)
  }
]

const loading = ref<{ status: boolean }>({ status: false })
</script>
<template>
  <ElCard shadow="hover" class="mb-20px">
    <ElSkeleton :loading="loading.status" animated>
      <div class="flex w-full items-center justify-center relative h-34px">
        <ElDatePicker
          v-if="store.datepicker"
          class="max-w-160px !absolute left-0"
          v-model="months"
          type="monthrange"
          :clearable="false"
          start-placeholder="Start month"
          end-placeholder="End month"
          :shortcuts="shortcuts"
        />
        <div style="padding-inline: 1rem; font-weight: bold"
          ><ElText line-clamp="1">{{ t('问题分布-按原因类型') }} </ElText></div
        >
        <ElDropdown trigger="click" class="!absolute right-0">
          <span
            class="el-dropdown-link"
            style="
              height: 2rem;
              width: 2rem;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 1.5rem;
              border: 1px solid #ddd;
            "
          >
            <!--            <svg
              v-if="chartTypeRef.type === 'column'"
              stroke="currentColor"
              fill="currentColor"
              stroke-width="0"
              viewBox="0 0 512 512"
              height="1em"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M332.8 320h38.4c6.4 0 12.8-6.4 12.8-12.8V172.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v134.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V76.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v230.4c0 6.4 6.4 12.8 12.8 12.8zm-288 0h38.4c6.4 0 12.8-6.4 12.8-12.8v-70.4c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v70.4c0 6.4 6.4 12.8 12.8 12.8zm96 0h38.4c6.4 0 12.8-6.4 12.8-12.8V108.8c0-6.4-6.4-12.8-12.8-12.8h-38.4c-6.4 0-12.8 6.4-12.8 12.8v198.4c0 6.4 6.4 12.8 12.8 12.8zM496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16z"
              />
            </svg>
            <svg
              v-if="chartTypeRef.type === 'line'"
              stroke="currentColor"
              fill="currentColor"
              stroke-width="0"
              viewBox="0 0 512 512"
              height="1em"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M496 384H64V80c0-8.84-7.16-16-16-16H16C7.16 64 0 71.16 0 80v336c0 17.67 14.33 32 32 32h464c8.84 0 16-7.16 16-16v-32c0-8.84-7.16-16-16-16zM464 96H345.94c-21.38 0-32.09 25.85-16.97 40.97l32.4 32.4L288 242.75l-73.37-73.37c-12.5-12.5-32.76-12.5-45.25 0l-68.69 68.69c-6.25 6.25-6.25 16.38 0 22.63l22.62 22.62c6.25 6.25 16.38 6.25 22.63 0L192 237.25l73.37 73.37c12.5 12.5 32.76 12.5 45.25 0l96-96 32.4 32.4c15.12 15.12 40.97 4.41 40.97-16.97V112c.01-8.84-7.15-16-15.99-16z"
              />
            </svg>
            <svg
              v-if="chartTypeRef.type === 'pie'"
              stroke="currentColor"
              fill="currentColor"
              stroke-width="0"
              viewBox="0 0 544 512"
              height="1em"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M527.79 288H290.5l158.03 158.03c6.04 6.04 15.98 6.53 22.19.68 38.7-36.46 65.32-85.61 73.13-140.86 1.34-9.46-6.51-17.85-16.06-17.85zm-15.83-64.8C503.72 103.74 408.26 8.28 288.8.04 279.68-.59 272 7.1 272 16.24V240h223.77c9.14 0 16.82-7.68 16.19-16.8zM224 288V50.71c0-9.55-8.39-17.4-17.84-16.06C86.99 51.49-4.1 155.6.14 280.37 4.5 408.51 114.83 513.59 243.03 511.98c50.4-.63 96.97-16.87 135.26-44.03 7.9-5.6 8.42-17.23 1.57-24.08L224 288z"
              />
            </svg>-->
            <svg
              v-if="chartTypeRef.type === 'radar'"
              stroke="currentColor"
              fill="currentColor"
              stroke-width="0"
              viewBox="0 0 1024 1024"
              height="1.4em"
              width="1.4em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M926.8 397.1l-396-288a31.81 31.81 0 0 0-37.6 0l-396 288a31.99 31.99 0 0 0-11.6 35.8l151.3 466a32 32 0 0 0 30.4 22.1h489.5c13.9 0 26.1-8.9 30.4-22.1l151.3-466c4.2-13.2-.5-27.6-11.7-35.8zM838.6 417l-98.5 32-200-144.7V199.9L838.6 417zM466 567.2l-89.1 122.3-55.2-169.2L466 567.2zm-116.3-96.8L484 373.3v140.8l-134.3-43.7zM512 599.2l93.9 128.9H418.1L512 599.2zm28.1-225.9l134.2 97.1L540.1 514V373.3zM558 567.2l144.3-46.9-55.2 169.2L558 567.2zm-74-367.3v104.4L283.9 449l-98.5-32L484 199.9zM169.3 470.8l86.5 28.1 80.4 246.4-53.8 73.9-113.1-348.4zM327.1 853l50.3-69h269.3l50.3 69H327.1zm414.5-33.8l-53.8-73.9 80.4-246.4 86.5-28.1-113.1 348.4z"
              />
            </svg>
          </span>
          <template #dropdown>
            <ElDropdownMenu>
              <ElDropdownItem @click="() => (chartTypeRef.type = 'radar')">
                <ElIcon
                  style="
                    height: 2rem;
                    width: 2rem;
                    font-size: 1.75rem;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                  "
                >
                  <svg
                    stroke="currentColor"
                    fill="currentColor"
                    stroke-width="0"
                    viewBox="0 0 1024 1024"
                    height="1.8em"
                    width="1.8em"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M926.8 397.1l-396-288a31.81 31.81 0 0 0-37.6 0l-396 288a31.99 31.99 0 0 0-11.6 35.8l151.3 466a32 32 0 0 0 30.4 22.1h489.5c13.9 0 26.1-8.9 30.4-22.1l151.3-466c4.2-13.2-.5-27.6-11.7-35.8zM838.6 417l-98.5 32-200-144.7V199.9L838.6 417zM466 567.2l-89.1 122.3-55.2-169.2L466 567.2zm-116.3-96.8L484 373.3v140.8l-134.3-43.7zM512 599.2l93.9 128.9H418.1L512 599.2zm28.1-225.9l134.2 97.1L540.1 514V373.3zM558 567.2l144.3-46.9-55.2 169.2L558 567.2zm-74-367.3v104.4L283.9 449l-98.5-32L484 199.9zM169.3 470.8l86.5 28.1 80.4 246.4-53.8 73.9-113.1-348.4zM327.1 853l50.3-69h269.3l50.3 69H327.1zm414.5-33.8l-53.8-73.9 80.4-246.4 86.5-28.1-113.1 348.4z"
                    />
                  </svg>
                </ElIcon>
              </ElDropdownItem>
            </ElDropdownMenu>
          </template>
        </ElDropdown>
        <!--        <ElSelect v-model="chartType">
          <ElOption value="column">column</ElOption>
          <ElOption value="line">line</ElOption>
          <ElOption value="pie">
            <ElIcon>
              <PieChart />
            </ElIcon>
          </ElOption>
        </ElSelect>-->
      </div>
      <Radar
        v-if="chartTypeRef.type === 'radar'"
        :start="months[0]"
        :end="months[1]"
        :loading="loading"
      />
      <!--      <Column
        v-if="chartTypeRef.type === 'column'"
        :start="months[0]"
        :end="months[1]"
        :loading="loading"
      />-->
    </ElSkeleton>
  </ElCard>
</template>
