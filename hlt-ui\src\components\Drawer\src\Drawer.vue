<script setup lang="ts">
import { useAppStore } from '@/store/modules/app'
import { propTypes } from '@/utils/propTypes'
import { ElDrawer } from 'element-plus'

const slots = useSlots()
const appStore = useAppStore()

const props = defineProps({
  modelValue: propTypes.bool.def(false),
  title: propTypes.string.def('Dialog'),
  fullscreen: propTypes.bool.def(true),
  defaultFullscreen: propTypes.bool.def(false),
  size: propTypes.oneOfType([String, Number]).def(400),
  closeOnPressEscape: propTypes.bool.def(false),
  closeOnClickModal: propTypes.bool.def(false)
})

const getBindValue = computed(() => {
  const delArr: string[] = ['fullscreen', 'title', 'size']
  const attrs = useAttrs()
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})

const size = ref(props.size)
const isFullscreen = ref(props.defaultFullscreen)

const toggleFull = () => {
  isFullscreen.value = !unref(isFullscreen)
}

watch(
  () => ({
    fullscreen: isFullscreen.value,
    collapse: appStore.getCollapse,
    mobile: appStore.getMobile
  }),
  async (val: { fullscreen: boolean; collapse: boolean; mobile: boolean }) => {
    await nextTick()
    if (val.fullscreen) {
      size.value = val.mobile ? '100%' : `calc(100vw - ${val.collapse ? 4 : 12.5}rem)`
    } else {
      size.value = props.size
    }
  },
  {
    immediate: true
  }
)
</script>

<template>
  <ElDrawer
    v-bind="getBindValue"
    :fullscreen="isFullscreen"
    :size="size"
    destroy-on-close
    append-to-body
    lock-scroll
    draggable
    :close-on-press-escape="closeOnPressEscape"
    top="0"
    :close-on-click-modal="closeOnClickModal"
    :show-close="false"
  >
    <template #header="{ close }">
      <div class="flex justify-between items-center h-54px relative w-full font-700 pl-15px">
        <slot name="title">
          {{ title }}
        </slot>
        <div
          class="h-54px flex justify-between items-center absolute top-[50%] right-15px translate-y-[-50%]"
        >
          <Icon
            v-if="fullscreen"
            class="cursor-pointer is-hover !h-54px mr-10px"
            :icon="isFullscreen ? 'radix-icons:exit-full-screen' : 'radix-icons:enter-full-screen'"
            color="var(--el-color-info)"
            hover-color="var(--el-color-primary)"
            @click="toggleFull"
          />
          <Icon
            class="cursor-pointer is-hover !h-54px"
            icon="ep:close"
            hover-color="var(--el-color-primary)"
            color="var(--el-color-info)"
            @click="close"
          />
        </div>
      </div>
    </template>
    <ElScrollbar class="h-full px-15px py-15px"> <slot></slot> </ElScrollbar>
    <template v-if="slots.footer" #footer>
      <slot name="footer"></slot>
    </template>
  </ElDrawer>
</template>

<style lang="less">
.@{elNamespace}-drawer {
  margin: 0 !important;

  &__header {
    height: 54px;
    padding: 0;
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    border-bottom: 1px solid var(--el-border-color);
  }

  &__body {
    padding: 0 !important;
  }

  &__footer {
    border-top: 1px solid var(--el-border-color);
    padding-top: 10px;
    padding-bottom: 10px;
    .@{elNamespace}-button {
      margin-top: 0 !important;
    }
  }

  &__headerbtn {
    top: 0;
  }
}
</style>
