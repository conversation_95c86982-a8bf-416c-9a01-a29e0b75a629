<script setup lang="tsx">
import { ContentWrap } from '@/components/ContentWrap'
import { useI18n } from '@/hooks/web/useI18n'
import { ElTabPane, ElTabs } from 'element-plus'
import Cqe from './components/Cqe.vue'
import Draft from './components/Draft.vue'
import List from './components/List.vue'

defineOptions({
  name: 'ProblemIndex'
})

const { t } = useI18n()
const tabName = ref('list')
onMounted(async () => {
  const fullHash = window.location.hash
  let search: string
  if (!!fullHash?.length) {
    const segements = fullHash.split('?')
    search = segements.slice(1).join('')
  } else {
    search = window.location.search
  }
  const hashParams = new URLSearchParams(search)
  const mode = hashParams.get('mode')
  if (mode === 'view') {
    tabName.value = 'list'
  }
})
</script>

<template>
  <ContentWrap :bodyStyle="{ height: 'calc(100vh - 8.25rem)' }">
    <ElTabs :active-name="tabName">
      <ElTabPane :label="t('草稿列表')" name="draft" lazy>
        <Draft />
      </ElTabPane>
      <ElTabPane :label="t('待分配列表')" name="cqe" lazy>
        <Cqe />
      </ElTabPane>
      <ElTabPane :label="t('问题列表')" name="list" lazy>
        <List />
      </ElTabPane>
    </ElTabs>
  </ContentWrap>
</template>
