<script setup lang="tsx">
import {
  downloadRelationApi,
  downloadTplApi,
  getListApi,
  saveOrUpdateApi,
  uploadApi,
  uploadRelation<PERSON><PERSON>
} from '@/api/dictionary'
import { Dictionary, DictionaryCategory, DictionaryOption } from '@/api/dictionary/types'
import { BaseButton } from '@/components/Button'
import { ContentWrap } from '@/components/ContentWrap'
import { Drawer } from '@/components/Drawer'
import { Search } from '@/components/Search'
import { Table, TableColumn } from '@/components/Table'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { useValidator } from '@/hooks/web/useValidator'
import { useResizeObserver } from '@vueuse/core'
import {
  ElSpace,
  ElTag,
  ElTooltip,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadUserFile,
  genFileId
} from 'element-plus'
import { saveAs } from 'file-saver'
import get from 'lodash-es/get'
import Write from './components/Write.vue'

defineOptions({
  name: 'Dictionary'
})

const { t } = useI18n()
const visible = ref(false)
const drawerVisible = ref(false)
const currentRow = ref<Dictionary | null>(null)
const exportRow = ref<Partial<Dictionary>>({})
const actionType = ref('')
const action = (row: Dictionary, type: string) => {
  actionType.value = type
  currentRow.value = row
  drawerVisible.value = true
}
const searchParams = ref({})
const setSearchParams = (params: any) => {
  searchParams.value = params
  getList()
}
const selectRows = ref<Dictionary[]>([])
const setSelectionChange = (rows: Dictionary[]) => {
  selectRows.value = rows
}
const { required, whitespace } = useValidator()

const crudSchemas = reactive<CrudSchema[]>([
  {
    field: 'selection',
    type: 'selection',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    }
  },
  {
    field: 'index',
    label: t('序号'),
    type: 'index',
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      slots: {
        default: ({ $index }: { $index: number }) => {
          const currentPage = tableState.currentPage.value || 1
          const pageSize = tableState.pageSize.value || 10
          const seq = (currentPage - 1) * pageSize + $index + 1
          return <span>{seq}</span>
        }
      }
    }
  },
  {
    field: 'name',
    label: t('字典名称'),
    table: {
      width: 120,
      sortable: true,
      sortMethod: (o1, o2) => {
        return t(o1.name).localeCompare(t(o2.name))
      },
      formatter: (_, __, cellValue: string) => {
        return t(cellValue)
      }
    },
    search: {
      component: 'Select',
      componentProps: { multiple: true, filterable: true, collapseTags: true },
      optionApi: async () => {
        const res = await getListApi()
        return res.data.map((dict) => ({ label: t(dict.name), value: dict.name }))
      }
    },
    form: {
      formItemProps: {
        slots: {
          default: ({ formValue }: any) => {
            return <span>{t(formValue.name)}</span>
          }
        }
      }
    },
    detail: {
      span: 24
    }
  },
  {
    field: 'priority',
    label: t('优先级'),
    table: {
      width: 100,
      sortable: true,
      sortMethod: (o1, o2) => {
        return o1.priority - o2.priority
      }
    },
    form: {
      component: 'InputNumber',
      colProps: { span: 24 },
      componentProps: {
        min: 0,
        max: 100,
        precision: 0
      }
    },
    search: {
      hidden: true
    }
  },
  {
    field: 'description',
    label: t('字典描述'),
    table: {
      sortable: true,
      sortMethod: (o1, o2) => {
        return o1.description.localeCompare(o2.description)
      }
    },
    form: {
      component: 'Input',
      colProps: { span: 24 },
      componentProps: {
        type: 'textarea',
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    search: {
      hidden: true
    }
  },
  {
    field: 'options',
    label: t('字典选项'),
    table: {
      showOverflowTooltip: false
    },
    search: {
      hidden: true
    },
    form: {
      component: 'FormList',
      colProps: { span: 24 },
      componentProps: {
        labelField: 'name',
        alwayShowLabel: false,
        children: [
          {
            field: 'name',
            label: t('选项名称'),
            component: 'Input',
            colProps: { span: 12 },
            formItemProps: {
              rules: [required(), whitespace()]
            }
          },
          {
            field: 'name',
            label: t('选项名称(当前语言)'),
            colProps: { span: 12 },
            formItemProps: {
              slots: {
                default: ({ formValue, schema }) => {
                  return <span>{t(get(formValue, schema.field))}</span>
                }
              }
            }
          }
        ]
      }
    },
    formatter: (row: Recordable, __: TableColumn, cellValue: DictionaryOption[] = []) => {
      const tree = row.tree === true
      return (
        <ElSpace wrap>
          {cellValue.map((item) => {
            if (tree && !!item.children?.length) {
              return (
                <ElTooltip
                  effect="light"
                  persistent
                  v-slots={{
                    content: () => {
                      return (
                        <ElSpace wrap>
                          {(item.children ?? []).map((child) => (
                            <ElTag>{t(child.name)}</ElTag>
                          ))}
                        </ElSpace>
                      )
                    }
                  }}
                >
                  <ElTag effect="dark" class={'cursor-pointer'}>
                    {t(item.name)}
                  </ElTag>
                </ElTooltip>
              )
            } else {
              return <ElTag effect="dark">{t(item.name)}</ElTag>
            }
          })}
        </ElSpace>
      )
    }
  }
  /* {
    field: 'action',
    width: 100,
    label: t('操作'),
    search: {
      hidden: true
    },
    form: {
      hidden: true
    },
    detail: {
      hidden: true
    },
    table: {
      slots: {
        default: (data: any) => {
          return (
            <>
              <BaseButton
                v-hasPermi="dictionary:edit"
                type="text"
                onClick={() => action(data.row, 'edit')}
              >
                {t('编辑')}
              </BaseButton>
            </>
          )
        }
      }
    }
  } */
])

const { allSchemas } = useCrudSchemas(crudSchemas)

const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const res = await getListApi(searchParams.value)
    return {
      list: res.data,
      total: res.data.length
    }
  }
})
const { loading, dataList } = tableState
const { getList, refresh } = tableMethods

const writeRef = ref<ComponentRef<typeof Write>>()
const saveLoading = ref(false)

const save = async () => {
  const write = unref(writeRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true
    const res = await saveOrUpdateApi(formData)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false
      })
    if (res) {
      drawerVisible.value = false
      getList()
    }
  }
}
const searchForm = ref(null)
useResizeObserver(searchForm, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const { height: pHeight = 0 } =
    entry.target.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - 82 })
})
const contentRef = ref(null)
useResizeObserver(contentRef, () => {
  const ele = unref(searchForm) as unknown as HTMLDivElement
  const { height } = ele.getBoundingClientRect() ?? {}
  const { height: pHeight = 0 } = ele.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - 82 })
})
const importData = (entity: Dictionary) => {
  visible.value = true
  exportRow.value = entity
}
const downloadTemplate = async (category?: DictionaryCategory) => {
  if (!!category) {
    await downloadTplApi(category)
  } else {
    await downloadRelationApi()
  }
}
const upload = ref<UploadInstance>()
const files = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const handleChange: UploadProps['onChange'] = (_, uploadFiles) => {
  files.value = uploadFiles
}
const errorFile = ref<{ url: string; name: string }>()
const submitUpload = async () => {
  if (files.value.length !== 1) {
    ElMessage.error('请上传一个文件')
    return
  }
  const [file] = files.value
  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.XLSX')) {
    ElMessage.error('请上传正确的模板文件')
    return
  }
  saveLoading.value = true
  try {
    const entity = unref(exportRow)
    const res = !!entity.category
      ? await uploadApi(entity.category, file)
      : await uploadRelationApi(file)
    if ((res as any).code === 0) {
      visible.value = false
      ElMessage.success('上传成功')
      refresh()
    } else if (!!(res as any).file) {
      errorFile.value = { ...(res as any).file }
    }
  } catch {
  } finally {
    saveLoading.value = false
  }
}
const importRelation = () => {
  visible.value = true
}
watch(
  () => visible.value,
  (visible) => {
    if (!visible) {
      if (!!exportRow.value.category) {
        exportRow.value = {}
      }
      errorFile.value = undefined
      files.value = []
    }
  }
)
</script>

<template>
  <ContentWrap ref="contentRef" :bodyStyle="{ height: 'calc(100vh - 8.25rem)' }">
    <div ref="searchForm">
      <Search
        :schema="allSchemas?.searchSchema"
        @search="setSearchParams"
        @reset="setSearchParams"
      />
    </div>
    <div class="mb-10px">
      <BaseButton
        v-hasPermi="'dictionary:edit'"
        :disabled="selectRows.length !== 1"
        type="primary"
        @click="action(selectRows[0], 'edit')"
      >
        {{ t('编辑') }}
      </BaseButton>
      <BaseButton
        :disabled="selectRows.length !== 1 || selectRows[0].category === DictionaryCategory.FACTORY"
        type="primary"
        @click="importData(selectRows[0])"
      >
        {{ t('导入') }}
      </BaseButton>
      <BaseButton :disabled="selectRows.length !== 0" type="primary" @click="importRelation">
        {{ t('导入机型关联') }}
      </BaseButton>
    </div>
    <Table
      align="center"
      headerAlign="center"
      @selection-change="setSelectionChange"
      :columns="allSchemas.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      @refresh="refresh"
    />
    <Dialog
      v-model="visible"
      :fullscreen="false"
      :title="`${t('导入')} ${t(exportRow.name ?? '机型关联')}`"
      :maxHeight="80"
    >
      <ElUpload
        ref="upload"
        accept=".xlsx"
        class="upload-demo"
        v-model="files"
        :limit="1"
        :on-change="handleChange"
        :on-exceed="handleExceed"
        :auto-upload="false"
      >
        <template #trigger>
          <el-button type="primary">{{ t('选择文件') }}</el-button>
        </template>
      </ElUpload>
      <template #footer>
        <BaseButton
          v-if="!!errorFile?.name.length"
          type="default"
          @click="
            () => {
              if (!!errorFile) {
                saveAs(errorFile.url, '错误文件.xlsx')
              }
            }
          "
        >
          {{ t('下载错误文件') }}
        </BaseButton>
        <BaseButton type="primary" :loading="saveLoading" @click="submitUpload">
          {{ t('上传') }}
        </BaseButton>
        <BaseButton type="default" @click="downloadTemplate(exportRow.category)">
          {{ t('下载模板') }}
        </BaseButton>
      </template>
    </Dialog>
    <Drawer
      v-model="drawerVisible"
      :title="`${t('编辑字典')}-${t(currentRow?.name ?? '')}`"
      :size="600"
    >
      <Write
        v-if="actionType !== 'detail'"
        ref="writeRef"
        :form-schema="allSchemas.formSchema"
        :current-row="currentRow"
      />

      <template #footer>
        <BaseButton
          v-if="actionType !== 'detail'"
          type="primary"
          :loading="saveLoading"
          @click="save"
        >
          {{ t('提交') }}
        </BaseButton>
        <BaseButton @click="drawerVisible = false">{{ t('关闭') }}</BaseButton>
      </template>
    </Drawer>
  </ContentWrap>
</template>
