<script setup lang="tsx">
import { Attachment, ID } from '@/api/base/types'
import { ReasonDetail, ReasonDetailOption, ReasonDetailType } from '@/api/problem/types'
import { deleteDetail, previewApi, saveOrUpdateDetail } from '@/api/task'
import { BaseButton } from '@/components/Button'
import { Icon } from '@/components/Icon'
import { TableColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { propTypes } from '@/utils/propTypes'
import { ElCollapse, ElCollapseItem, ElLink, ElPopconfirm, ElSpace, UploadFile } from 'element-plus'
import EditForm from './EditForm.vue'
const { t } = useI18n()
const emit = defineEmits(['change'])
const model = defineModel<Record<ReasonDetailType, ReasonDetail[]>>({
  type: Object as PropType<Record<ReasonDetailType, ReasonDetail[]>>,
  default: () => {}
})
const props = defineProps({
  entityId: {
    type: Number as PropType<ID>
  },
  readonly: propTypes.bool.def(false)
})
const tableConfigs = ref<Record<ReasonDetailType, any>>({
  [ReasonDetailType.EXPOSE]: {},
  [ReasonDetailType.PRODUCE]: {},
  [ReasonDetailType.SYSTEM]: {}
})
const dialogVisible = ref(false)
const currentRow = ref<Partial<ReasonDetail & { file?: UploadFile }> | null>(null)
const actionType = ref('')
const saveLoading = ref(false)
const action = async (row: Partial<ReasonDetail>, type: string) => {
  actionType.value = type
  if (type === 'add' || type === 'edit') {
    currentRow.value = {
      ...row,
      file: !!row.attachment
        ? ({
            name: row.attachment.filename,
            status: 'success',
            size: row.attachment.size,
            uid: row.id
          } as UploadFile)
        : undefined
    }
    dialogVisible.value = true
  } else {
    try {
      const res = await deleteDetail(props.entityId!, [row.id!])
      if (!!res) {
        emit('change')
      }
    } catch {}
  }
}
const tableColumns = reactive<TableColumn[]>([
  {
    field: 'index',
    label: t('序号'),
    type: 'index'
  },
  {
    field: 'question',
    minWidth: 150,
    label: t('问题')
  },
  {
    field: 'answer',
    minWidth: 150,
    label: t('答案')
  },
  {
    field: 'evidence',
    minWidth: 150,
    label: t('证据')
  },
  {
    field: 'attachment',
    label: t('附件'),
    formatter: (row, __, cellValue: Attachment) => {
      return !!cellValue ? (
        <ElLink
          type="primary"
          onClick={() => {
            previewApi(row.id, cellValue.key)
          }}
        >
          <ElSpace>
            <Icon icon="ant-design:download-outlined" />
            {cellValue.filename}
          </ElSpace>
        </ElLink>
      ) : (
        <>-</>
      )
    }
  }
])
if (!props.readonly) {
  tableColumns.push({
    field: 'action',
    label: t('操作'),
    width: 140,
    fixed: 'right',
    slots: {
      default: (data: any) => {
        return (
          <>
            <BaseButton type="text" onClick={() => action(data.row, 'edit')}>
              {t('编辑')}
            </BaseButton>
            <ElPopconfirm
              title={t('是否确认删除')}
              onConfirm={() => action(data.row, 'del')}
              v-slots={{
                reference: () => {
                  return (
                    <BaseButton type="danger" link>
                      {t('删除')}
                    </BaseButton>
                  )
                }
              }}
            />
          </>
        )
      }
    }
  })
}
const formRef = ref<ComponentRef<typeof EditForm>>()
const save = async () => {
  const write = unref(formRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true
    try {
      const { file, ...rest } = formData
      const res = await saveOrUpdateDetail(props.entityId!, rest, file)
      if (res) {
        emit('change')
        dialogVisible.value = false
      }
    } catch {
    } finally {
      saveLoading.value = false
    }
  }
}

watch(
  () => model.value,
  (val) => {
    for (const key of Object.keys(val ?? {})) {
      const { tableRegister, tableMethods, tableState } = useTable({
        fetchDataApi: async () => {
          const data = val[key]
          return {
            list: data,
            total: data.length
          }
        }
      })
      tableConfigs.value[key] = {
        tableRegister,
        tableMethods,
        tableState
      }
      tableConfigs.value[key].tableMethods.refresh()
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <ElCollapse class="w-full" :model-value="Object.keys(model ?? {})">
    <ElCollapseItem name="EXPOSE" :title="t(`${ReasonDetailOption[ReasonDetailType.EXPOSE]}`)">
      <div class="mb-10px" v-if="!readonly">
        <BaseButton type="primary" @click="action({ type: ReasonDetailType.EXPOSE }, 'add')"
          >{{ t('新增') }}
        </BaseButton>
      </div>
      <Table
        v-if="tableConfigs[ReasonDetailType.EXPOSE]?.tableState"
        align="center"
        headerAlign="center"
        :columns="tableColumns"
        :data="tableConfigs[ReasonDetailType.EXPOSE].tableState.dataList"
        @register="tableConfigs[ReasonDetailType.EXPOSE].tableRegister"
        @refresh="tableConfigs[ReasonDetailType.EXPOSE].tableMethods.refresh"
      />
    </ElCollapseItem>
    <ElCollapseItem name="PRODUCE" :title="t(`${ReasonDetailOption[ReasonDetailType.PRODUCE]}`)">
      <div class="mb-10px" v-if="!readonly">
        <BaseButton type="primary" @click="action({ type: ReasonDetailType.PRODUCE }, 'add')"
          >{{ t('新增') }}
        </BaseButton>
      </div>
      <Table
        v-if="tableConfigs[ReasonDetailType.PRODUCE]?.tableState"
        align="center"
        headerAlign="center"
        :columns="tableColumns"
        :data="tableConfigs[ReasonDetailType.PRODUCE].tableState.dataList"
        @register="tableConfigs[ReasonDetailType.PRODUCE].tableRegister"
        @refresh="tableConfigs[ReasonDetailType.PRODUCE].tableMethods.refresh"
      />
    </ElCollapseItem>
    <ElCollapseItem name="SYSTEM" :title="t(`${ReasonDetailOption[ReasonDetailType.SYSTEM]}`)">
      <div class="mb-10px" v-if="!readonly">
        <BaseButton type="primary" @click="action({ type: ReasonDetailType.SYSTEM }, 'add')"
          >{{ t('新增') }}
        </BaseButton>
      </div>
      <Table
        v-if="tableConfigs[ReasonDetailType.SYSTEM]?.tableState"
        align="center"
        headerAlign="center"
        :columns="tableColumns"
        :data="tableConfigs[ReasonDetailType.SYSTEM].tableState.dataList"
        @register="tableConfigs[ReasonDetailType.SYSTEM].tableRegister"
        @refresh="tableConfigs[ReasonDetailType.SYSTEM].tableMethods.refresh"
      />
    </ElCollapseItem>
  </ElCollapse>
  <Dialog v-model="dialogVisible" :title="t('原因明细')">
    <EditForm ref="formRef" :current-row="currentRow" />

    <template #footer>
      <BaseButton type="primary" :loading="saveLoading" @click="save()">
        {{ !!currentRow?.id ? t('保存') : t('添加') }}
      </BaseButton>
    </template>
  </Dialog>
</template>
