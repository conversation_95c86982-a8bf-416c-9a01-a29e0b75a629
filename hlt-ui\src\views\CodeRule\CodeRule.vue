<script setup lang="tsx">
import { delete<PERSON>pi, getList<PERSON><PERSON>, saveOrUpdate<PERSON><PERSON> } from '@/api/codeRule'
import { CodeRule } from '@/api/codeRule/types'
import { getList<PERSON>pi as getDictionaries } from '@/api/dictionary'
import { BaseButton } from '@/components/Button'
import { ContentWrap } from '@/components/ContentWrap'
import { Drawer } from '@/components/Drawer'
import { Search } from '@/components/Search'
import { Table } from '@/components/Table'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { ElMessage, ElPopconfirm } from 'element-plus'
import Write from './components/Write.vue'
import { Operators } from '@/utils'
import { useResizeObserver } from '@vueuse/core'
import { convertRem } from '@/rem'

defineOptions({
  name: 'CodeRule'
})

const { t } = useI18n()

const drawerVisible = ref(false)
const currentRow = ref<CodeRule | null>(null)
const actionType = ref('')
const action = (row: CodeRule | null, type: string) => {
  actionType.value = type
  currentRow.value = row
  drawerVisible.value = true
}
const searchParams = ref({})
const defaultSortProps: { prop: string; order: 'ascending' | 'descending' | null } = {
  prop: 'id',
  order: 'ascending'
}
const sortParams = ref<{ prop: string; order: 'ascending' | 'descending' | null }>(defaultSortProps)
const setSearchParams = (params: any) => {
  searchParams.value = params
  getList()
}

const setSortChange = ({
  prop,
  order
}: {
  prop: string
  order: 'ascending' | 'descending' | null
}) => {
  sortParams.value = order === null ? defaultSortProps : { prop, order }
  getList()
}

const allSchemas = ref<Recordable>()

onBeforeMount(async () => {
  const res = await getDictionaries()
  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection',
      type: 'selection',
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      }
    },
    {
      field: 'index',
      label: t('序号'),
      type: 'index',
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      }
    },
    {
      field: 'dictionary.id',
      label: t('类型'),
      table: { hidden: true },
      search: { hidden: true },
      detail: { hidden: true },
      form: {
        component: 'Select',
        colProps: { span: 24 },
        componentProps: {
          filterable: true,
          options: res.data.map((dict) => ({
            label: t(dict.name),
            value: dict.id,
            items: dict.options.map((option) => ({ label: t(option.name), value: option.name }))
          }))
        }
      }
    },
    {
      field: 'dictionary.name',
      label: t('类型'),
      table: {
        width: 120,
        sortable: 'custom',
        formatter: (_, __, cellValue: string) => {
          return t(cellValue)
        }
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          filterable: true,
          collapseTags: true,
          options: res.data.map((dict) => ({ label: t(dict.name), value: dict.name }))
        }
      },
      form: {
        hidden: true
      },
      detail: {
        span: 24
      }
    },
    {
      field: 'option',
      label: t('名称'),
      table: {
        sortable: 'custom',
        formatter: (_, __, value) => {
          return <>{t(value)}</>
        }
      },
      search: {
        hidden: true
      },
      form: {
        component: 'Select',
        componentProps: { filterable: true },
        formItemProps: { rules: [] },
        colProps: { span: 24 }
      }
    },
    {
      field: 'code',
      label: t('编码'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      },
      form: {
        component: 'Input',
        colProps: { span: 24 },
        componentProps: { maxLength: 10 }
      }
    },
    {
      field: 'description',
      label: t('说明'),
      table: {
        sortable: 'custom'
      },
      form: {
        component: 'Input',
        colProps: { span: 24 },
        componentProps: {
          type: 'textarea',
          allowClear: true,
          autosize: { minRows: 4, maxRows: 6 }
        }
      },
      search: {
        hidden: true
      }
    }
    /*  {
      field: 'action',
      width: 100,
      label: t('操作'),
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      },
      table: {
        slots: {
          default: (data: any) => {
            return (
              <>
                <BaseButton
                  type="text"
                  v-hasPermi="coderule:edit"
                  onClick={() => action(data.row, 'edit')}
                >
                  {t('编辑')}
                </BaseButton>
              </>
            )
          }
        }
      }
    } */
  ])
  allSchemas.value = useCrudSchemas(crudSchemas).allSchemas
})

const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const { currentPage, pageSize } = tableState
    const res = await getListApi({
      query: unref(searchParams),
      page: { pageNo: unref(currentPage), pageSize: unref(pageSize) },
      sort: unref(sortParams)
    })
    const { data } = res
    return {
      list: data.data,
      total: data.total
    }
  }
})
const { total, currentPage, pageSize, loading, dataList } = tableState
const { getList, refresh } = tableMethods

const writeRef = ref<ComponentRef<typeof Write>>()
const saveLoading = ref(false)
const selectRows = ref<CodeRule[]>([])
const setSelectionChange = (rows: CodeRule[]) => {
  selectRows.value = rows
}
const deleteEntity = async () => {
  loading.value = true
  const rows = unref(selectRows)
  try {
    await deleteApi(rows.map((item) => item.id))
    ElMessage.success(t('删除成功'))
    selectRows.value = []
    getList()
  } catch {
  } finally {
    loading.value = false
  }
}
const save = async () => {
  const write = unref(writeRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true
    const res = await saveOrUpdateApi(formData)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false
      })
    if (res) {
      drawerVisible.value = false
      getList()
    }
  }
}
const searchForm = ref(null)
useResizeObserver(searchForm, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const { height: pHeight = 0 } =
    entry.target.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
const contentRef = ref(null)
useResizeObserver(contentRef, () => {
  const ele = unref(searchForm) as unknown as HTMLDivElement
  const { height } = ele.getBoundingClientRect() ?? {}
  const { height: pHeight = 0 } = ele.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
</script>

<template>
  <ContentWrap ref="contentRef" :bodyStyle="{ height: 'calc(100vh - 8.25rem)' }">
    <div ref="searchForm">
      <Search
        :schema="allSchemas?.searchSchema"
        @search="setSearchParams"
        @reset="setSearchParams"
      />
    </div>
    <div class="mb-10px">
      <BaseButton v-hasPermi="'coderule:add'" type="primary" @click="action(null, 'add')"
        >{{ t('新增') }}
      </BaseButton>
      <BaseButton
        v-hasPermi="'coderule:edit'"
        :disabled="selectRows.length !== 1"
        type="primary"
        @click="action(selectRows[0], 'edit')"
      >
        {{ t('编辑') }}
      </BaseButton>
      <ElPopconfirm :title="t('是否确认删除')" @confirm="deleteEntity" :width="200">
        <template #reference>
          <BaseButton
            :loading="loading"
            v-hasPermi="'coderule:delete'"
            type="danger"
            :disabled="!selectRows.length"
          >
            {{ t('删除') }}
          </BaseButton>
        </template>
      </ElPopconfirm>
    </div>

    <Table
      align="center"
      headerAlign="center"
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      @sort-change="setSortChange"
      @selection-change="setSelectionChange"
      :pagination="{ total }"
      :columns="allSchemas?.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      @refresh="refresh"
    />
    <Drawer
      v-model="drawerVisible"
      :title="`${t((currentRow?.id ? '编辑' : '新增') + '编码规则')}`"
    >
      <Write
        v-if="actionType !== 'detail'"
        ref="writeRef"
        :form-schema="allSchemas?.formSchema"
        :current-row="currentRow"
      />

      <template #footer>
        <BaseButton
          v-if="actionType !== 'detail'"
          type="primary"
          :loading="saveLoading"
          @click="save"
        >
          {{ t('提交') }}
        </BaseButton>
        <BaseButton @click="drawerVisible = false">{{ t('关闭') }}</BaseButton>
      </template>
    </Drawer>
  </ContentWrap>
</template>
