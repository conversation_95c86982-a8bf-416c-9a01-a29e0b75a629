<script setup lang="ts">
import { ConfigGlobal } from '@/components/ConfigGlobal'
import { useDesign } from '@/hooks/web/useDesign'
import { useAppStore } from '@/store/modules/app'
import { isDark } from '@/utils/is'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { getTodoApi } from './api/dashboard'
import { getPermissions, getProfile, loginByTokenApi } from './api/login'
import { UserType } from './api/login/types'
import { useTodoStore } from './store/modules/todo'
import { useUserStore } from './store/modules/user'
import { useLocaleStore } from './store/modules/locale'
import { useLocale } from '@/hooks/web/useLocale'

const localeStore = useLocaleStore()

dayjs.extend(utc)
dayjs.extend(timezone)

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('app')

const appStore = useAppStore()
const todoStore = useTodoStore()

const currentSize = computed(() => appStore.getCurrentSize)

const greyMode = computed(() => appStore.getGreyMode)
const userStore = useUserStore()
const setDefaultTheme = () => {
  const app = localStorage.getItem('app')
  if (app) {
    return
  }
  const isDarkTheme = isDark()
  appStore.setIsDark(isDarkTheme)
}
onMounted(async () => {
  // setTimeout(() => {
  //   ;(document.body.style as any).zoom = 0.9
  // }, 300)
  ;(window as any).__inited__ = false
  const fullHash = window.location.hash
  let search: string
  if (!!fullHash?.length) {
    if (fullHash.startsWith('#from_login') && fullHash.indexOf('?') === -1) {
      userStore.setRedirect(true)
      await getProfile()
      return
    }
    const segements = fullHash.split('?')
    search = segements.slice(1).join('')
  } else {
    search = window.location.search
  }
  const hashParams = new URLSearchParams(search)
  const token = hashParams.get('authKey')
  const redirect = hashParams.get('redirect')
  const tk = hashParams.get('token')
  const router = useRouter()
  const title = import.meta.env.VITE_APP_TITLE
  if (appStore.getTitle !== title) {
    appStore.setTitle(title)
  }
  if (!!token?.length) {
    if (!!userStore.getUserInfo) {
      userStore.logout(false)
    }
    const res = await loginByTokenApi(token)
    if (res.data.token) {
      userStore.setToken(res.data.token)
    }
    if (res.data.user) {
      userStore.setUserInfo(res.data.user as UserType)
      const { data } = await getPermissions()
      userStore.setSysRouters(data.routes)
      userStore.setPermissions(data.buttons)
    }
    if (localeStore.getCurrentLocale.lang !== userStore.getLang) {
      const { changeLocale } = useLocale()
      await changeLocale(userStore.getLang)
    }
    setTimeout(() => {
      if (!!redirect) {
        router.push(redirect)
      } else {
        router.push(router.currentRoute.value.path)
      }
    }, 500)
  } else if (!!tk?.length && !userStore.getUserInfo) {
    const res = await loginByTokenApi(tk)
    if (res.data.token) {
      userStore.setToken(res.data.token)
    }
    if (res.data.user) {
      userStore.setUserInfo(res.data.user as UserType)
      const { data } = await getPermissions()
      userStore.setSysRouters(data.routes)
      userStore.setPermissions(data.buttons)
    }
  } else if (!!userStore.getUserInfo) {
    try {
      const { data } = await getProfile()
      userStore.setLang(data.languageCode)
      if (localeStore.getCurrentLocale.lang !== userStore.getLang) {
        const { changeLocale } = useLocale()
        await changeLocale(userStore.getLang)
      }
    } catch (e) {
      userStore.logout(false)
    }
  }

  if (!!userStore.getUserInfo) {
    const { data } = await getTodoApi()
    todoStore.setTodo(data)
  }
  ;(window as any).__inited__ = true
})
setDefaultTheme()
</script>

<template>
  <ConfigGlobal :size="currentSize">
    <RouterView :class="greyMode ? `${prefixCls}-grey-mode` : ''" />
  </ConfigGlobal>
</template>

<style lang="less">
@prefix-cls: ~'@{namespace}-app';

.size {
  width: 100%;
  height: 100%;
}

html,
body {
  padding: 0 !important;
  margin: 0;
  overflow: hidden;
  .size;

  #app {
    .size;
  }
}

.@{prefix-cls}-grey-mode {
  filter: grayscale(100%);
}
</style>
