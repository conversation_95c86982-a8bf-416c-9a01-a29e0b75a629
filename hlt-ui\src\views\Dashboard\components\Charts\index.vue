<script setup lang="ts">
import { ElCol, ElRow } from 'element-plus'
import Filter from './Filter.vue'
import ProductStepChart from './ProductStepChart/index.vue'
import ProductCustomerChart from './ProductCustomerChart/index.vue'
import ProductReasonChart from './ProductReasonChart/index.vue'
import ProductBadTypeChart from './ProductBadTypeChart/index.vue'
import TableDialog from '@/views/Dashboard/components/Charts/TableDialog.vue'
import PanelDialog from '@/views/Dashboard/components/Charts/PanelDialog.vue'
import { useDashboardStore } from '@/store/modules/dashboard'

import echarts from '@/plugins/echarts'
import { ScatterChart } from 'echarts/charts'
import { TimelineComponent, TransformComponent } from 'echarts/components'

echarts.use([ScatterChart, TimelineComponent, TransformComponent])
const store = useDashboardStore()
store.datepicker = true
</script>
<template>
  <TableDialog />
  <PanelDialog />
  <Suspense>
    <Filter />
  </Suspense>
  <ElRow :gutter="20" justify="space-between" style="margin-top: 0.75rem">
    <ElCol :sm="24" :md="12">
      <ProductStepChart />
    </ElCol>
    <ElCol :sm="24" :md="12">
      <ProductCustomerChart />
    </ElCol>
    <ElCol :sm="24" :md="12">
      <ProductReasonChart />
    </ElCol>
    <ElCol :sm="24" :md="12">
      <ProductBadTypeChart />
    </ElCol>
  </ElRow>
</template>
