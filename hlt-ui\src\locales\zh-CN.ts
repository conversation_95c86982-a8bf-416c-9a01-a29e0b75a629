export default {
  common: {
    inputText: '请输入',
    selectText: '请选择',
    startTimeText: '开始时间',
    endTimeText: '结束时间',
    login: '登录',
    required: '该项为必填项',
    loginOut: '退出系统',
    document: '项目文档',
    reminder: '温馨提示',
    loginOutMessage: '是否退出本系统？',
    back: '返回',
    ok: '确定',
    cancel: '取消',
    reload: '重新加载',
    closeTab: '关闭标签页',
    closeTheLeftTab: '关闭左侧标签页',
    closeTheRightTab: '关闭右侧标签页',
    closeOther: '关闭其它标签页',
    closeAll: '关闭全部标签页',
    prevLabel: '上一步',
    nextLabel: '下一步',
    skipLabel: '跳过',
    doneLabel: '结束',
    menu: '菜单',
    menuDes: '以路由的结构渲染的菜单栏',
    collapse: '展开缩收',
    collapseDes: '展开和缩放菜单栏',
    tagsView: '标签页',
    tagsViewDes: '用于记录路由历史记录',
    tool: '工具',
    toolDes: '用于设置定制系统',
    query: '查询',
    reset: '重置',
    shrink: '收起',
    expand: '展开',
    delMessage: '是否删除所选中数据？',
    delWarning: '提示',
    delOk: '确定',
    delCancel: '取消',
    delNoData: '请选择需要删除的数据',
    delSuccess: '删除成功',
    refresh: '刷新',
    fullscreen: '全屏',
    size: '尺寸',
    columnSetting: '列设置',
    lengthRange: '长度在 {min} 到 {max} 个字符',
    notSpace: '不能包含空格',
    notSpecialCharacters: '不能包含特殊字符',
    isEqual: '两次输入不一致',
    save: '保存',
    saveFailure: '保存失败',
    view: '查看',
    obsolete: '作废'
  },
  lock: {
    lockScreen: '锁定屏幕',
    lock: '锁定',
    lockPassword: '锁屏密码',
    unlock: '点击解锁',
    backToLogin: '返回登录',
    entrySystem: '进入系统',
    placeholder: '请输入锁屏密码',
    message: '锁屏密码错误'
  },
  error: {
    noPermission: `抱歉，您无权访问此页面。`,
    pageError: '抱歉，您访问的页面不存在。',
    networkError: '抱歉，服务器报告错误。',
    returnToHome: '返回首页'
  },
  setting: {
    projectSetting: '项目配置',
    theme: '主题',
    layout: '布局',
    systemTheme: '系统主题',
    menuTheme: '菜单主题',
    interfaceDisplay: '界面显示',
    breadcrumb: '面包屑',
    breadcrumbIcon: '面包屑图标',
    collapseMenu: '折叠菜单',
    hamburgerIcon: '折叠图标',
    screenfullIcon: '全屏图标',
    sizeIcon: '尺寸图标',
    localeIcon: '多语言图标',
    tagsView: '标签页',
    logo: '标志',
    greyMode: '灰色模式',
    fixedHeader: '固定头部',
    headerTheme: '头部主题',
    cutMenu: '切割菜单',
    copy: '拷贝',
    clearAndReset: '清除缓存并且重置',
    copySuccess: '拷贝成功',
    copyFailed: '拷贝失败',
    footer: '页脚',
    uniqueOpened: '菜单手风琴',
    tagsViewIcon: '标签页图标',
    dynamicRouter: '开启动态路由',
    serverDynamicRouter: '服务端动态路由',
    reExperienced: '请重新退出登录体验',
    fixedMenu: '固定菜单'
  },
  size: {
    default: '默认',
    large: '大',
    small: '小'
  },
  login: {
    welcome: '欢迎使用本系统',
    message: '开箱即用的中后台管理系统',
    username: '用户名',
    password: '密码',
    register: '注册',
    checkPassword: '确认密码',
    login: '登录',
    otherLogin: '其它登录方式',
    remember: '记住我',
    hasUser: '已有账号？去登录',
    forgetPassword: '忘记密码',
    usernamePlaceholder: '请输入用户名',
    passwordPlaceholder: '请输入密码',
    code: '验证码',
    codePlaceholder: '请输入验证码'
  },
  router: {
    login: '登录',
    level: '多级菜单',
    menu: '菜单',
    menu1: '菜单1',
    menu11: '菜单1-1',
    menu111: '菜单1-1-1',
    menu12: '菜单1-2',
    menu2: '菜单2',
    dashboard: '首页',
    analysis: '分析页',
    workplace: '工作台',
    guide: '引导',
    component: '组件',
    icon: '图标',
    echart: '图表',
    countTo: '数字动画',
    watermark: '水印',
    qrcode: '二维码',
    highlight: '高亮',
    infotip: '信息提示',
    form: '表单',
    defaultForm: '全部示例',
    search: '查询',
    table: '表格',
    defaultTable: '基础示例',
    editor: '编辑器',
    richText: '富文本',
    jsonEditor: 'JSON编辑器',
    dialog: '弹窗',
    imageViewer: '图片预览',
    descriptions: '描述',
    example: '综合示例',
    exampleDialog: '综合示例 - 弹窗',
    examplePage: '综合示例 - 页面',
    exampleAdd: '综合示例 - 新增',
    exampleEdit: '综合示例 - 编辑',
    exampleDetail: '综合示例 - 详情',
    errorPage: '错误页面',
    authorization: '权限管理',
    user: '用户管理',
    role: '角色管理',
    document: '文档',
    inputPassword: '密码输入框',
    sticky: '黏性',
    treeTable: '树形表格',
    PicturePreview: '表格图片预览',
    department: '部门管理',
    menuManagement: '菜单管理',
    permission: '权限测试页',
    function: '功能',
    multipleTabs: '多开标签页',
    details: '详情页',
    iconPicker: '图标选择器',
    request: '请求',
    waterfall: '瀑布流',
    imageCropping: '图片裁剪',
    videoPlayer: '视频播放器',
    tableVideoPreview: '表格视频预览',
    cardTable: '卡片表格',
    basic: {
      index: '基础数据',
      dictionary: '字典管理',
      'code-rule': '编码规则'
    }
  },
  permission: {
    hasPermission: '请设置操作权限值'
  },
  analysis: {
    open: 'Open',
    follow: 'Follow',
    processing: '待处理',
    newUser: '新增用户',
    unreadInformation: '未读消息',
    transactionAmount: '成交金额',
    totalShopping: '购物总量',
    monthlySales: '每月销售额',
    userAccessSource: '用户访问来源',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',
    estimate: '预计',
    actual: '实际',
    directAccess: '直接访问',
    mailMarketing: '邮件营销',
    allianceAdvertising: '联盟广告',
    videoAdvertising: '视频广告',
    searchEngines: '搜索引擎',
    weeklyUserActivity: '每周用户活跃量',
    activeQuantity: '活跃量',
    monday: '周一',
    tuesday: '周二',
    wednesday: '周三',
    thursday: '周四',
    friday: '周五',
    saturday: '周六',
    sunday: '周日'
  },
  workplace: {
    goodMorning: '早安',
    happyDay: '祝你开心每一天!',
    toady: '今日晴',
    project: '项目数',
    access: '项目访问',
    toDo: '待办',
    introduction: '一个正经的简介',
    more: '更多',
    shortcutOperation: '快捷操作',
    operation: '操作',
    index: '指数',
    personal: '个人',
    team: '团队',
    quote: '引用',
    contribution: '贡献',
    hot: '热度',
    yield: '产量',
    dynamic: '动态',
    push: '推送',
    pushCode: 'Archer 推送 代码到 Github',
    follow: '关注'
  },
  formDemo: {
    input: '输入框',
    inputNumber: '数字输入框',
    default: '默认',
    icon: '图标',
    mixed: '复合型',
    password: '密码框',
    textarea: '多行文本',
    remoteSearch: '远程搜索',
    slot: '插槽',
    position: '位置',
    autocomplete: '自动补全',
    select: '选择器',
    optionSlot: '选项插槽',
    selectGroup: '选项分组',
    selectV2: '虚拟列表选择器',
    cascader: '级联选择器',
    switch: '开关',
    rate: '评分',
    colorPicker: '颜色选择器',
    transfer: '穿梭框',
    render: '渲染器',
    radio: '单选框',
    radioGroup: '单选框组',
    button: '按钮',
    checkbox: '多选框',
    checkboxButton: '多选框按钮',
    checkboxGroup: '多选框组',
    slider: '滑块',
    datePicker: '日期选择器',
    shortcuts: '快捷选项',
    today: '今天',
    yesterday: '昨天',
    aWeekAgo: '一周前',
    week: '周',
    year: '年',
    month: '月',
    dates: '日期',
    daterange: '日期范围',
    monthrange: '月份范围',
    dateTimePicker: '日期时间选择器',
    dateTimerange: '日期时间范围',
    timePicker: '时间选择器',
    timeSelect: '时间选择',
    inputPassword: '密码输入框',
    passwordStrength: '密码强度',
    defaultForm: '全部示例',
    formDes: '基于 ElementPlus 的 Form 组件二次封装，实现数据驱动，支持所有 Form 参数',
    example: '示例',
    operate: '操作',
    change: '更改',
    restore: '还原',
    disabled: '禁用',
    disablement: '解除禁用',
    delete: '删除',
    add: '添加',
    setValue: '设置值',
    resetValue: '重置值',
    set: '设置',
    subitem: '子项',
    formValidation: '表单验证',
    verifyReset: '验证重置',
    // 富文本编辑器
    richText: '富文本编辑器',
    // JSON编辑器
    jsonEditor: 'JSON编辑器',
    form: '表单',
    // 远程加载
    remoteLoading: '远程加载',
    // 聚焦
    focus: '聚焦',
    treeSelect: '树形选择器',
    showCheckbox: '显示复选框',
    selectAnyLevel: '选择任意级别',
    multiple: '多选',
    filterable: '可筛选',
    customContent: '自定义内容',
    lazyLoad: '懒加载',
    upload: '上传',
    userAvatar: '用户头像',
    iconPicker: '图标选择器'
  },
  guideDemo: {
    guide: '引导页',
    start: '开始',
    message:
      '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。引导页基于 driver.js'
  },
  iconDemo: {
    icon: '图标',
    localIcon: '本地图标',
    iconify: 'Iconify组件',
    recommendedUse: '推荐使用',
    recommendeDes:
      'Iconify组件基本包含所有的图标，你可以查询到你想要的任何图标。并且打包只会打包所用到的图标。',
    accessAddress: '访问地址'
  },
  echartDemo: {
    echart: '图表',
    echartDes:
      '基于 echarts 二次封装组件，自适应宽度，只需传入 options 与 height 属性即可展示对应的图表。'
  },
  countToDemo: {
    countTo: '数字动画',
    countToDes: '基于 vue-count-to 进行改造，支持所有 vue-count-to 参数。',
    suffix: '后缀',
    prefix: '前缀',
    separator: '分割符号',
    duration: '持续时间',
    endVal: '结束值',
    startVal: '开始值',
    start: '开始',
    pause: '暂停',
    resume: '继续'
  },
  watermarkDemo: {
    watermark: '水印',
    createdWatermark: '创建水印',
    clearWatermark: '清除水印',
    resetWatermark: '重置水印'
  },
  qrcodeDemo: {
    qrcode: '二维码',
    qrcodeDes: '基于 qrcode 二次封装',
    basicUsage: '基础用法',
    imgTag: 'img标签',
    style: '样式配置',
    click: '点击事件',
    asynchronousContent: '异步内容',
    invalid: '失效',
    logoConfig: 'logo配置',
    logoStyle: 'logo样式',
    size: '大小配置'
  },
  highlightDemo: {
    highlight: '高亮',
    message: '种一棵树最好的时间是十年前，其次就是现在。',
    keys1: '十年前',
    keys2: '现在'
  },
  infotipDemo: {
    infotip: '信息提示',
    infotipDes: '基于 Highlight 组件二次封装',
    title: '注意事项'
  },
  levelDemo: {
    menu: '多级菜单缓存'
  },
  searchDemo: {
    search: '查询',
    searchDes: '基于 Form 组件二次封装，实现查询、重置功能',
    operate: '操作',
    change: '更改',
    grid: '栅格',
    button: '按钮',
    restore: '还原',
    inline: '内联',
    bottom: '底部',
    position: '位置',
    left: '左',
    center: '中',
    right: '右',
    dynamicOptions: '动态选项',
    // 删除单选框
    deleteRadio: '删除单选框',
    // 还原单选框
    restoreRadio: '还原单选框',
    loading: '加载中',
    reset: '重置'
  },
  stickyDemo: {
    sticky: '黏性'
  },
  tableDemo: {
    table: '表格',
    tableDes: '基于 ElementPlus 的 Table 组件二次封装',
    index: '序号',
    title: '标题',
    author: '作者',
    displayTime: '创建时间',
    importance: '重要性',
    pageviews: '阅读数',
    action: '操作',
    important: '重要',
    good: '良好',
    commonly: '一般',
    operate: '操作',
    example: '示例',
    show: '显示',
    hidden: '隐藏',
    pagination: '分页',
    reserveIndex: '叠加序号',
    restoreIndex: '还原序号',
    showSelections: '显示多选',
    hiddenSelections: '隐藏多选',
    showExpandedRows: '显示展开行',
    hiddenExpandedRows: '隐藏展开行',
    changeTitle: '修改标题',
    header: '头部',
    selectAllNone: '全选/全不选',
    delOrAddAction: '删除/添加操作列',
    showOrHiddenStripe: '显示/隐藏斑马纹',
    showOrHiddenBorder: '显示/隐藏边框',
    fixedHeaderOrAuto: '固定头部/自动',
    getSelections: '获取多选数据',
    preview: '封面',
    showOrHiddenSortable: '显示/隐藏排序',
    videoPreview: '视频预览',
    cardTable: '卡片表格'
  },
  richText: {
    richText: '富文本',
    richTextDes: '基于 wangeditor 二次封装',
    jsonEditor: 'JSON编辑器',
    jsonEditorDes: '基于 vue-json-pretty 二次封装'
  },
  dialogDemo: {
    dialog: '弹窗',
    dialogDes: '基于 ElementPlus 的 Dialog 组件二次封装',
    open: '打开',
    close: '关闭',
    combineWithForm: '与表单结合',
    submit: '提交'
  },
  imageViewerDemo: {
    open: '打开',
    imageViewer: '图片预览',
    imageViewerDes: '基于 ElementPlus 的 ImageViewer 组件二次封装'
  },
  descriptionsDemo: {
    descriptions: '描述',
    descriptionsDes: '基于 ElementPlus 的 Descriptions 组件二次封装',
    username: '用户名',
    nickName: '昵称',
    phone: '联系电话',
    email: '邮箱',
    addr: '地址',
    form: '与 Form 组件组合'
  },
  exampleDemo: {
    title: '标题',
    add: '新增',
    del: '删除',
    edit: '编辑',
    author: '作者',
    displayTime: '创建时间',
    importance: '重要性',
    pageviews: '阅读数',
    important: '重要',
    content: '内容',
    save: '保存',
    detail: '详情'
  },
  userDemo: {
    title: '用户管理',
    message: '由于是模拟数据，所以只提供了两种不同权限的帐号，开发者可根据实际情况自行改造结合。',
    index: '序号',
    action: '操作',
    username: '用户名',
    password: '密码',
    role: '角色',
    remark: '备注',
    remarkMessage1: '后端控制路由权限',
    remarkMessage2: '前端控制路由权限',
    // 部门列表
    departmentList: '部门列表',
    searchDepartment: '搜索部门',
    account: '账号',
    email: '邮箱',
    createTime: '创建时间',
    // 所属部门
    department: '所属部门',
    departmentName: '部门名称',
    status: '状态',
    // 启用
    enable: '启用',
    // 禁用
    disable: '禁用',
    // 上级部门
    superiorDepartment: '上级部门'
  },
  menu: {
    menuName: '菜单名称',
    icon: '图标',
    permission: '按钮权限',
    component: '组件',
    path: '路径',
    status: '状态',
    hidden: '是否隐藏',
    alwaysShow: '是否一直显示',
    noCache: '是否清除缓存',
    breadcrumb: '是否显示面包屑',
    affix: '是否固定在标签页',
    noTagsView: '是否隐藏标签页',
    activeMenu: '高亮菜单',
    canTo: '是否可跳转',
    name: '组件名称'
  },
  role: {
    roleName: '角色名称',
    role: '角色',
    menu: '菜单分配'
  },
  inputPasswordDemo: {
    title: '密码输入框',
    inputPasswordDes: '基于 ElementPlus 的 Input 组件二次封装'
  },
  problem: {
    tab1: '草稿',
    tab2: '问题',
    reason: {
      category: '原因类别(一级)',
      subCategory: '原因类别(二级)',
      unqualityType: '不良类别',
      improvement: '行动计划',
      status: '状态',
      estimatedFinishOn: '预计完成日期',
      finishOn: '实际完成日期',
      responsible: '责任人',
      responsibleDep: '部门',
      validateResult: '验证结果',
      validateOn: '验证时间'
    },
    images: {
      good: '好件',
      bad: '坏件',
      limit: '只能上传图片文件',
      goodTips: '上传同一视角好件照片',
      badTips: '上传同一视角坏件照片'
    },
    form: {
      tab1: '问题描述',
      tab2: '原因分析',
      tab3: '流程明细'
    },
    btn: {
      process: '处理',
      audit: '审核',
      approve: '批准',
      reject: '驳回'
    },
    reasonDetail: {
      PRODUCE: '产生原因',
      EXPOSE: '流出原因',
      SYSTEM: '系统原因'
    }
  }
}
