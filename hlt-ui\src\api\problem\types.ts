import dayjs from 'dayjs'
import { Attachment, BaseEntity, ID } from './../base/types'

export enum ReasonDetailType {
  PRODUCE = 'PRODUCE',
  EXPOSE = 'EXPOSE',
  SYSTEM = 'SYSTEM'
}

export const ReasonDetailOption = {
  PRODUCE: '产生原因',
  EXPOSE: '流出原因',
  SYSTEM: '系统原因'
}
export enum ReasonStatus {
  OPEN = 'OPEN',
  FOLLOW = 'FOLLOW',
  CLOSED = 'CLOSED',
  REJECTED = 'REJECTED'
}

export enum NodeState {
  ANALYZE = 'ANALYZE',
  ANALYZE_AUDIT = 'ANALYZE_AUDIT',
  VALIDATE = 'VALIDATE',
  VALIDATE_AUDIT = 'VALIDATE_AUDIT',
  CQE_AUDIT = 'CQE_AUDIT',
  COMPLETE = 'COMPLETE'
}

export enum ProblemStatus {
  DRAFT = 'DRAFT',
  CQE = 'CQE',
  NEW = 'NEW',
  PROCESSING = 'PROCESSING',
  CLOSED = 'CLOSED',
  OBSOLETE = 'OBSOLETE'
}

export const ProblemStatusOptions = [
  { value: 'CQE', label: '待分配' },
  { value: 'NEW', label: '待处理' },
  { value: 'PROCESSING', label: '处理中' },
  { value: 'CLOSED', label: '已关闭' },
  { value: 'OBSOLETE', label: '已作废' }
]

export const ProblemStatusRecords: Record<ProblemStatus, string> = {
  [ProblemStatus.DRAFT]: '草稿',
  [ProblemStatus.CQE]: '待分配',
  [ProblemStatus.NEW]: '待处理',
  [ProblemStatus.PROCESSING]: '处理中',
  [ProblemStatus.CLOSED]: '已关闭',
  [ProblemStatus.OBSOLETE]: '已作废'
}

export const ReasonStateOptions = {
  ANALYZE: '原因分析',
  ANALYZE_AUDIT: '原因分析审核',
  VALIDATE: '效果验证(CQE)',
  VALIDATE_AUDIT: '效果验证审核',
  CQE_AUDIT: '最终审核',
  COMPLETE: '完成'
}

export const ReasonStateSelectOptions = [
  { value: 'ANALYZE', label: '原因分析' },
  { value: 'ANALYZE_AUDIT', label: '原因分析审核' },
  { value: 'VALIDATE', label: '效果验证' },
  { value: 'VALIDATE_AUDIT', label: '效果验证审核' },
  { value: 'CQE_AUDIT', label: '最终审核' },
  { value: 'COMPLETE', label: '完成' }
]

export type ReasonDetail = BaseEntity & {
  question: string //问题
  answer: string //答案
  evidence: string //证据
  attachment?: Attachment //附件
  type: ReasonDetailType
}

export type Node = BaseEntity & {
  index: number
  prev?: NodeState
  current: NodeState
  next?: NodeState
}

export type ReasonConfig = {
  ownerId: ID
  ownerName: string
  ownerDepartment: string
  ownerEmail?: string
  state?: NodeState
  stateIdx?: number
  copy?: 'ANALYZE' | 'VALIDATE'
}

export type Reason = BaseEntity & {
  category: string //原因类别(一级)
  subCategory: string //原因类别(二级)
  unqualityType: string //不良类别
  unqualityCode: string //不良代码
  details: ReasonDetail[] // 原因详情
  configs: ReasonConfig[] // 流程配置
  improvement: string //行动计划
  validateResult: string //验证结果
  estimatedFinishOn?: dayjs.Dayjs | string //预计完成日期
  finishOn?: dayjs.Dayjs | string //实际完成日期
  status: ReasonStatus //原因状态
  state: NodeState //当前节点
  stateIdx: number
  delete: boolean
  remark?: string //驳回理由
}

export type ProblemOperateLog = BaseEntity & {
  problemId: ID //问题ID
  reasonID?: ID //原因ID
  operatorId: ID // 操作人ID
  operatorName: string //操作人姓名
  createdAt: dayjs.Dayjs | string
  action: { key: string; message: string; params: Record<string, any> }
  before: string
  after: string
}

export type ProblemDescription = {
  what: string
  why: string
  where: string
  when: string
  who: string
  how_detected: string
  how_many: string
}

export type Problem = BaseEntity & {
  code: string
  createdOn: dayjs.Dayjs | string //创建时间
  machineType: string //机型
  customer: string //客户
  projectCode: string //项目编号
  productLine: string //生产线
  factory: string //工厂
  businessUnit: string // 事业部
  creatorId: ID //创建人ID
  creatorName: string //创建人姓名
  productStep: string //产品阶段
  status: ProblemStatus //项目状态  新建/处理中/结束
  description: string //问题描述
  descriptions: ProblemDescription //问题描述(5w2h)
  goodPartImages: Attachment[] //好件附件
  badPartImages: Attachment[] //坏件附件
  workOrderCode: string
  workOrderNum: string
  reasons: Reason[]
  cqeId?: string | number
  cqeName?: string
  extCode?: string
}
