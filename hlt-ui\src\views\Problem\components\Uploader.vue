<script setup lang="tsx">
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { propTypes } from '@/utils/propTypes'
import { createImageViewer } from '@/components/ImageViewer'
import {
  ElUpload,
  UploadFile,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  genFileId
} from 'element-plus'

const { t } = useI18n()
const props = defineProps({
  good: propTypes.bool.def(true)
})
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('uploader')

const upload = ref<UploadInstance>()

const emit = defineEmits(['submit'])
const handlePreview = (file: UploadFile) => {
  if (!!file.url) {
    createImageViewer({
      urlList: [file.url],
      zIndex: 9999
    })
  }
}
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const good = computed(() => props.good)
const files = ref<UploadFile[]>([])
const wrongType = ref<boolean>(false)

const submit = () => {
  const [file] = unref(files)
  if (!!file) {
    emit('submit', file)
  }
}
const handleChange = (type: 'change' | 'remove' = 'change', file: UploadFile) => {
  if (type === 'remove') {
    files.value = []
    wrongType.value === false
  } else {
    wrongType.value = file.raw?.type.startsWith('image') === false
    files.value = [file]
  }
}
</script>
<template>
  <ElUpload
    ref="upload"
    listType="picture-card"
    accept="image/*"
    :file-list="files"
    :class="`${prefixCls} w-full h-300px ${files.length === 0 ? '' : 'append-hidden'}`"
    :limit="1"
    :auto-upload="false"
    :on-preview="handlePreview"
    :on-change="(file) => handleChange('change', file)"
    :on-remove="(file) => handleChange('remove', file)"
    :on-exceed="handleExceed"
  >
    <div :class="`el-upload__text ${good ? 'good' : 'bad'}`">
      {{ t(good ? '上传同一视角好件照片' : '上传同一视角坏件照片') }}
    </div>
    <template #tip>
      <div class="el-upload__tip">
        <div class="flex justify-between items-center">
          <span v-if="wrongType" class="text-[var(--el-color-error)]">{{
            t('problem.images.limit')
          }}</span>
          <span v-else></span>
          <BaseButton type="primary" :disabled="files.length === 0 || wrongType" @click="submit"
            >{{ t('保存') }}
          </BaseButton>
        </div>
      </div>
    </template>
  </ElUpload>
</template>
<style lang="less">
@prefix-cls: ~'@{namespace}-uploader';

.@{prefix-cls} {
  .el-upload-list {
    width: 100%;
    height: calc(100% - 2rem);

    &__item {
      width: 100%;
      height: 100%;
      margin: 0;
    }

    .el-upload--picture-card {
      width: 100%;
      height: 100%;
    }

    .el-upload__text {
      font-size: 24px;

      &.good {
        color: var(--el-color-success-light-3);
      }

      &.bad {
        color: var(--el-color-error-light-3);
      }
    }
  }

  &.append-hidden {
    .el-upload--picture-card {
      display: none;
    }
  }
}
</style>
