export type ID = string | number

export type BaseEntity = {
  id: ID
}

export type BaseNameEntity = BaseEntity & {
  name: string
}

export type BaseCodeNameEntity = BaseNameEntity & {
  code: string
}

export type Page<D extends Record<string, any>> = {
  total: number
  pageNo: number
  pageSize: number
  data: D[]
}

export type Result<D = any> = {
  code: string
  message: string
  data: D
}

export type PartialEntity<E extends BaseEntity> = BaseEntity & Partial<Omit<E, 'id'>>

export type PartEntity<T extends BaseEntity, K extends keyof T> = BaseEntity &
  Pick<T, K> &
  Partial<Omit<T, 'id' | K>>

export type Attachment = {
  bucket: string
  extension: string
  filename: string
  key: string
  size: number
  type: string
}
