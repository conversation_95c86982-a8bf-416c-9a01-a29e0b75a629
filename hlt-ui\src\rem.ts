const baseSize = 16
function setRem() {
  const scale = document.documentElement.clientWidth / 1920
  const fontSize = baseSize * Math.min(scale, 2)
  document.documentElement.style.fontSize = fontSize + 'px'
}
/* setRem()
window.onresize = function () {
  setRem()
} */
function convertRem(val: number) {
  const scale = document.documentElement.clientWidth / 1920
  const fontSize = baseSize * Math.min(scale, 2)
  return (val / baseSize) * fontSize
}
export { convertRem, setRem }
