<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { getOptionByCategoryApi } from '@/api/dictionary'
import { DictionaryCategory, DictionaryOption } from '@/api/dictionary/types'
import { Department, User } from '@/api/user/types'
import { ComponentNameEnum, Form, FormSchema } from '@/components/Form'
import { StaffPicker } from '@/components/Form/src/components/StaffPicker'
import { useForm } from '@/hooks/web/useForm'
import { useI18n } from '@/hooks/web/useI18n'
import { useValidator } from '@/hooks/web/useValidator'
import { isUnDef } from '@/utils/is'
import { PropType } from 'vue'

const { required } = useValidator()
const { t } = useI18n()
export type Staff = {
  id: ID
  name: string
  department: string
  email?: string
}
export type ReasonDto = {
  id: ID
  category: string
  subCategory: string
  anlayzer: Staff
  anlayzerAudit: Staff
  anlayzerCopy: Staff
  validator: Staff
  validatorAudit: Staff
  validatorCopy: Staff
  cqeAudit: Staff
}

const props = defineProps({
  currentRow: {
    type: Object as PropType<Nullable<Partial<ReasonDto>>>,
    default: () => null
  },
  dictionary: {
    type: Array as PropType<DictionaryOption[]>,
    default: () => []
  },
  users: {
    type: Array as PropType<User[]>,
    default: () => []
  },
  departments: {
    type: Array as PropType<Department[]>,
    default: () => []
  }
})
const rules = reactive({
  /*category: [required()]
     createdOn: [required()],
  businessUnit: [required()],
  factory: [required()],
  customer: [required()],
  machineType: [required()],
  productStep: [required()],
  productLine: [required()],
  projectCode: [required(), whitespace()],
  description: [required(), whitespace()] */
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose, setSchema, getFieldValue } = formMethods
const schemas = ref<FormSchema[]>([])
onBeforeMount(async () => {
  const { data } = await getOptionByCategoryApi(DictionaryCategory.REASON_TYPE)
  schemas.value = [
    {
      field: 'category',
      label: t('原因类别(一级)'),
      component: ComponentNameEnum.SELECT,
      formItemProps: { rules: [required()] },
      componentProps: {
        filterable: true,
        onChange: async (open) => {
          await setSchema([
            {
              field: 'subCategory',
              path: 'componentProps.options',
              value: (data.find((item) => item.name === open)?.children ?? []).map((item) => ({
                label: t(item.name),
                value: item.name
              }))
            }
          ])
        },
        options: data.map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'subCategory',
      label: t('原因类别(二级)'),
      component: ComponentNameEnum.SELECT,
      formItemProps: { rules: [required()] },
      componentProps: {
        filterable: true,
        options: []
      }
    },
    {
      field: 'anlayzer.id',
      label: t('责任人(原因分析)'),
      colProps: { span: 8 },
      formItemProps: {
        rules: [
          {
            asyncValidator: (rule) => {
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  const val = await getFieldValue(rule.field!)
                  if (isUnDef(val)) {
                    reject(t('该项为必填项'))
                  } else {
                    resolve()
                  }
                }, 100)
              })
            }
          }
        ],
        slots: {
          default: ({ formValue }: any) => {
            const { anlayzer } = formValue as ReasonDto
            return (
              <StaffPicker
                user={anlayzer?.id}
                users={props.users}
                departments={props.departments}
                onChange={({ user, leader, indirectLeader }) => {
                  formValue.anlayzer = {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    department: props.departments.find((item) => item.id === user.deptId)?.name
                  }
                  if (!!leader) {
                    formValue.anlayzerAudit = {
                      id: leader.id,
                      name: leader.name,
                      email: leader.email,
                      department: props.departments.find((item) => item.id === leader.deptId)?.name
                    }
                  } else {
                    formValue.anlayzerAudit = { id: undefined }
                  }
                  if (!!indirectLeader) {
                    formValue.anlayzerCopy = {
                      id: indirectLeader.id,
                      name: indirectLeader.name,
                      email: indirectLeader.email,
                      department: props.departments.find(
                        (item) => item.id === indirectLeader.deptId
                      )?.name
                    }
                  } else {
                    formValue.anlayzerCopy = { id: undefined }
                  }
                }}
              />
            )
          }
        }
      }
    },
    {
      field: 'anlayzerAudit.id',
      label: t('直接领导(原因分析)'),
      colProps: { span: 8 },
      formItemProps: {
        rules: [
          {
            asyncValidator: (rule) => {
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  const val = await getFieldValue(rule.field!)
                  if (isUnDef(val)) {
                    reject(t('该项为必填项'))
                  } else {
                    resolve()
                  }
                }, 100)
              })
            }
          }
        ],
        slots: {
          default: ({ formValue }: any) => {
            const { anlayzerAudit } = formValue as ReasonDto
            return (
              <StaffPicker
                user={anlayzerAudit?.id}
                users={props.users}
                departments={props.departments}
                onChange={({ user, leader }) => {
                  formValue.anlayzerAudit = {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    department: props.departments.find((item) => item.id === user.deptId)?.name
                  }
                  if (!!leader) {
                    formValue.anlayzerCopy = {
                      id: leader.id,
                      name: leader.name,
                      email: leader.email,
                      department: props.departments.find((item) => item.id === leader.deptId)?.name
                    }
                  } else {
                    formValue.anlayzerCopy = { id: undefined }
                  }
                }}
              />
            )
          }
        }
      }
    },
    {
      field: 'anlayzerCopy.id',
      label: t('部门总监(原因分析)'),
      colProps: { span: 8 },
      formItemProps: {
        rules: [
          {
            asyncValidator: (rule) => {
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  const val = await getFieldValue(rule.field!)
                  if (isUnDef(val)) {
                    reject(t('该项为必填项'))
                  } else {
                    resolve()
                  }
                }, 100)
              })
            }
          }
        ],
        slots: {
          default: ({ formValue }: any) => {
            const { anlayzerCopy } = formValue as ReasonDto
            return (
              <StaffPicker
                user={anlayzerCopy?.id}
                users={props.users}
                departments={props.departments}
                onChange={({ user }) => {
                  formValue.anlayzerCopy = {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    department: props.departments.find((item) => item.id === user.deptId)?.name
                  }
                }}
              />
            )
          }
        }
      }
    },
    {
      field: 'validator.id',
      label: t('责任人(效果验证)'),
      colProps: { span: 8 },
      formItemProps: {
        rules: [
          {
            asyncValidator: (rule) => {
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  const val = await getFieldValue(rule.field!)
                  if (isUnDef(val)) {
                    reject(t('该项为必填项'))
                  } else {
                    resolve()
                  }
                }, 100)
              })
            }
          }
        ],
        slots: {
          default: ({ formValue }: any) => {
            const { validator } = formValue as ReasonDto
            return (
              <StaffPicker
                user={validator?.id}
                users={props.users}
                departments={props.departments}
                onChange={({ user, leader, indirectLeader }) => {
                  formValue.validator = {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    department: props.departments.find((item) => item.id === user.deptId)?.name
                  }
                  if (!!leader) {
                    formValue.validatorAudit = {
                      id: leader.id,
                      name: leader.name,
                      email: leader.email,
                      department: props.departments.find((item) => item.id === leader.deptId)?.name
                    }
                  } else {
                    formValue.validatorAudit = {
                      id: undefined
                    }
                  }
                  if (!!indirectLeader) {
                    formValue.validatorCopy = {
                      id: indirectLeader.id,
                      name: indirectLeader.name,
                      email: indirectLeader.email,
                      department: props.departments.find(
                        (item) => item.id === indirectLeader.deptId
                      )?.name
                    }
                  } else {
                    formValue.validatorCopy = {
                      id: undefined
                    }
                  }
                }}
              />
            )
          }
        }
      }
    },
    {
      field: 'validatorAudit.id',
      label: t('任务发起人直接领导(效果验证)'),
      colProps: { span: 8 },
      formItemProps: {
        rules: [
          {
            asyncValidator: (rule) => {
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  const val = await getFieldValue(rule.field!)
                  if (isUnDef(val)) {
                    reject(t('该项为必填项'))
                  } else {
                    resolve()
                  }
                }, 100)
              })
            }
          }
        ],
        slots: {
          default: ({ formValue }: any) => {
            const { validatorAudit } = formValue as ReasonDto
            return (
              <StaffPicker
                user={validatorAudit?.id}
                users={props.users}
                departments={props.departments}
                onChange={async ({ user, leader }) => {
                  formValue.validatorAudit = {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    department: props.departments.find((item) => item.id === user.deptId)?.name
                  }
                  if (!!leader) {
                    formValue.validatorCopy = {
                      id: leader.id,
                      name: leader.name,
                      email: leader.email,
                      department: props.departments.find((item) => item.id === leader.deptId)?.name
                    }
                  } else {
                    formValue.validatorCopy = {
                      id: undefined
                    }
                  }
                }}
              />
            )
          }
        }
      }
    },
    {
      field: 'validatorCopy.id',
      label: t('任务发起人部门总监(效果验证)'),
      colProps: { span: 8 },
      formItemProps: {
        required: true,
        rules: [
          {
            asyncValidator: (rule) => {
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  const val = await getFieldValue(rule.field!)
                  if (isUnDef(val)) {
                    reject(t('该项为必填项'))
                  } else {
                    resolve()
                  }
                }, 100)
              })
            }
          }
        ],
        slots: {
          default: ({ formValue }: any) => {
            const { validatorCopy } = formValue as ReasonDto
            return (
              <StaffPicker
                user={validatorCopy?.id}
                users={props.users}
                departments={props.departments}
                onChange={async ({ user }) => {
                  formValue.validatorCopy = {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    department: props.departments.find((item) => item.id === user.deptId)?.name
                  }
                }}
              />
            )
          }
        }
      }
    },
    {
      field: 'cqeAudit.id',
      label: t('CQE经理审批(效果验证)'),
      colProps: { span: 8 },
      formItemProps: {
        required: true,
        rules: [
          {
            asyncValidator: (rule) => {
              return new Promise((resolve, reject) => {
                setTimeout(async () => {
                  const val = await getFieldValue(rule.field!)
                  if (isUnDef(val)) {
                    reject(t('该项为必填项'))
                  } else {
                    resolve()
                  }
                }, 100)
              })
            }
          }
        ],
        slots: {
          default: ({ formValue }: any) => {
            const { cqeAudit } = formValue as ReasonDto
            return (
              <StaffPicker
                user={cqeAudit?.id}
                users={props.users}
                departments={props.departments}
                onChange={async ({ user }) => {
                  formValue.cqeAudit = {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    department: props.departments.find((item) => item.id === user.deptId)?.name
                  }
                }}
              />
            )
          }
        }
      }
    }
  ]
})

const submit = async (skipValidate: boolean = false) => {
  const elForm = await getElFormExpose()
  const valid =
    skipValidate ||
    (await elForm?.validate().catch((err) => {
      console.log(err)
    }))
  if (valid) {
    const formData = await getFormData<ReasonDto>()
    return formData
  }
}

watch(
  () => props.currentRow,
  (currentRow) => {
    if (!currentRow) return
    setValues(currentRow)
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})
</script>

<template>
  <Form
    :hideRequiredAsterisk="true"
    labelPosition="top"
    :rules="rules"
    :schema="schemas"
    @register="formRegister"
  />
</template>
