<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { getLogApi } from '@/api/problem'
import { ProblemOperateLog } from '@/api/problem/types'
import { DescriptionsSchema } from '@/components/Descriptions'
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { propTypes } from '@/utils/propTypes'
import dayjs from 'dayjs'
import { ElTimeline, ElTimelineItem } from 'element-plus'

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('log')

const { t } = useI18n()
const props = defineProps({
  isShow: propTypes.bool.def(false),
  id: String as PropType<Nullable<ID>>
})
const logs = ref<ProblemOperateLog[]>([])
watchEffect(async () => {
  if (!!props.id) {
    const { data = [] } = await getLogApi(props.id)
    logs.value = data
  }
})
const schemas: DescriptionsSchema[] = reactive([
  {
    field: 'logs',
    span: 24,
    slots: {
      default: (data) => {
        return (
          <ElTimeline>
            {data.map((datum) => {
              return (
                <ElTimelineItem
                  type="primary"
                  hollow
                  timestamp={dayjs(datum.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                >{`${datum.operatorName}${t(datum.action.message)}`}</ElTimelineItem>
              )
            })}
          </ElTimeline>
        )
      }
    }
  }
])
</script>
<template>
  <Descriptions
    :is-show="isShow"
    :class="prefixCls"
    :title="t('流程明细')"
    :data="logs"
    :schema="schemas"
  />
</template>
<style lang="less" scoped>
@prefix-cls: ~'@{elNamespace}-descriptions';

.@{prefix-cls} {
  :deep(&__label) {
    display: none;
  }
}
</style>
