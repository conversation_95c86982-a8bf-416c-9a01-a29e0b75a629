import { SysRouter } from './../../api/login/types'
import { defineStore } from 'pinia'
import { asyncRouterMap, constantRouterMap } from '@/router'
import {
  generateRoutesByFrontEnd,
  generateRoutesByServer,
  flatMultiLevelRoutes
} from '@/utils/routerHelper'
import { store } from '../index'
import { cloneDeep } from 'lodash-es'

export interface PermissionState {
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  isAddRouters: boolean
  menuTabRouters: AppRouteRecordRaw[]
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routers: [],
    addRouters: [],
    isAddRouters: false,
    menuTabRouters: []
  }),
  getters: {
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getIsAddRouters(): boolean {
      return this.isAddRouters
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    }
  },
  actions: {
    generateRoutes(
      type: 'server' | 'frontEnd' | 'static',
      routers?: SysRouter[] | AppCustomRouteRecordRaw[] | string[],
      permission?: string[]
    ): Promise<void> {
      return new Promise<void>((resolve) => {
        let routerMap: AppRouteRecordRaw[] = []
        if (type === 'server') {
          // 模拟后端过滤菜单
          routerMap = generateRoutesByServer(routers as AppCustomRouteRecordRaw[])
        } else if (type === 'frontEnd') {
          // 模拟前端过滤菜单
          routerMap = generateRoutesByFrontEnd(cloneDeep(asyncRouterMap), routers as string[])
        } else {
          const _routerMap = cloneDeep(asyncRouterMap)
          if (Array.isArray(routers)) {
            const filterRoute = (items: AppRouteRecordRaw[], parent?: AppRouteRecordRaw) => {
              let parentId: number | undefined = undefined
              if (!!parent) {
                parentId = (routers as SysRouter[]).find(
                  (item: SysRouter) => item.name.toLowerCase() === parent.name.toLowerCase()
                )?.id
              }
              const result = items.filter((item) => {
                return (routers as SysRouter[])
                  .filter(
                    (router) => (!parentId && !router.parentId) || parentId === router.parentId
                  )
                  .some((router) => router.name.toLowerCase() === item.name.toLowerCase())
              })
              for (const datum of result) {
                if (!!datum.children?.length) {
                  const keepChildren = datum.children.filter((item) => item.path === '')
                  const newChildren = filterRoute(
                    datum.children.filter((item) => item.path !== ''),
                    datum
                  )
                  if (!!keepChildren?.length) {
                    keepChildren.forEach((item) => {
                      item.meta.permission = (permission ?? []).filter((item) =>
                        item.toLocaleLowerCase().startsWith(`${datum.name.toLocaleLowerCase()}:`)
                      )
                    })
                  } else {
                    newChildren.forEach((child) => {
                      child.meta.permission = (permission ?? []).filter((item) =>
                        item.toLocaleLowerCase().startsWith(`${child.name.toLocaleLowerCase()}:`)
                      )
                    })
                  }
                  datum.children = [...keepChildren, ...newChildren]
                }
              }
              return result
            }
            routerMap = filterRoute(_routerMap)
          } else {
            // 直接读取静态路由表
            routerMap = _routerMap
          }
        }
        // 动态路由，404一定要放到最后面
        this.addRouters = routerMap.concat([
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ])
        // 渲染菜单的所有路由
        this.routers = cloneDeep(constantRouterMap).concat(routerMap)
        resolve()
      })
    },
    setIsAddRouters(state: boolean): void {
      this.isAddRouters = state
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    }
  },
  persist: {
    storage: window.sessionStorage,
    paths: ['routers', 'addRouters', 'menuTabRouters']
  }
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}
