<script setup lang="ts">
import { ID } from '@/api/base/types'
import { duplicateApi } from '@/api/codeRule'
import { CodeRule } from '@/api/codeRule/types'
import { Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { useI18n } from '@/hooks/web/useI18n'
import { useValidator } from '@/hooks/web/useValidator'

const { required, whitespace } = useValidator()
const { t } = useI18n()
const props = defineProps({
  currentRow: {
    type: Object as PropType<Nullable<CodeRule>>,
    default: () => null
  },
  formSchema: {
    type: Array as PropType<FormSchema[]>,
    default: () => []
  }
})

const rules = reactive({
  'dictionary.id': [required()],
  option: [required()],
  code: [required(), whitespace()],
  description: [whitespace()]
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose, setSchema, setFieldValue, getFieldValue } =
  formMethods

const submit = async () => {
  const elForm = await getElFormExpose()
  const valid = await elForm?.validate().catch((err) => {
    console.log(err)
  })
  if (valid) {
    const result = await getFormData<CodeRule>(false)
    if (!result.description?.length) {
      Object.assign(result, { description: null })
    }
    return result
  }
}

const schemas = computed(() => {
  const dictSchema = props.formSchema.find((item) => item.field === 'dictionary.id')
  if (dictSchema) {
    const { options } = dictSchema.componentProps
    if (!!props.currentRow?.dictionary.id) {
      setSchema([
        {
          field: 'option',
          path: 'componentProps.options',
          value:
            options.find((option: any) => option.value === props.currentRow!.dictionary.id)
              ?.items ?? []
        }
      ])
    }
    dictSchema.componentProps.onChange = async (value?: ID) => {
      await setSchema([
        {
          field: 'option',
          path: 'componentProps.options',
          value: !!value ? (options.find((option: any) => option.value === value)?.items ?? []) : []
        }
      ])
      await setFieldValue('option', undefined)
    }
  }
  setSchema([
    {
      field: 'option',
      path: 'formItemProps.rules',
      value: [
        {
          asyncValidator: async (_, value) => {
            if (!!value?.length) {
              const dictionaryId = await getFieldValue('dictionary.id')
              if (!!dictionaryId) {
                const duplicate = await duplicateApi({
                  id: props.currentRow?.id,
                  dictionary: { id: dictionaryId },
                  option: value
                })
                if (duplicate.data) {
                  throw `${t('此类型下相同选项已存在')}[${t(value)}]`
                }
              }
            }
          }
        }
      ]
    }
  ])
  return props.formSchema
})
watch(
  () => props.currentRow,
  (currentRow) => {
    if (!currentRow) return
    setValues(currentRow)
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})
</script>

<template>
  <Form labelPosition="top" :rules="rules" :schema="schemas" @register="formRegister" />
</template>
