<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { delReasonApi, getReasonList<PERSON>pi, saveReasonApi } from '@/api/problem'
import {
  NodeState,
  Problem,
  Reason,
  ReasonConfig,
  ReasonDetail,
  ReasonDetailType
} from '@/api/problem/types'
import { getDepartmentsApi, getUsersApi } from '@/api/user'
import { Department, User } from '@/api/user/types'
import { BaseButton } from '@/components/Button'
import { ContentWrap } from '@/components/ContentWrap'
import { Table, TableColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { propTypes } from '@/utils/propTypes'
import { ElPopconfirm, ElTag } from 'element-plus'
import ReasonDetailForm from './ReasonDetailForm.vue'
import ReasonForm, { ReasonDto, Staff } from './ReasonForm.vue'
const { t } = useI18n()

const model = defineModel<{ valid: boolean }>({
  type: Object as PropType<{ valid: boolean }>,
  default: () => ({ valid: false })
})

const props = defineProps({
  id: String as PropType<ID>,
  problem: Object as PropType<Problem>,
  readonly: propTypes.bool.def(false),
  draft: propTypes.bool.def(true)
})
const genFormData = (row: Reason) => {
  const {
    id,
    configs,
    details,
    category,
    subCategory,
    improvement,
    unqualityType,
    unqualityCode,
    estimatedFinishOn,
    validateResult,
    state,
    stateIdx
  } = row
  const config = configs.find((item) => item.state === NodeState.ANALYZE)
  const detailRecord: Record<ReasonDetailType, ReasonDetail[]> = {
    [ReasonDetailType.EXPOSE]: [],
    [ReasonDetailType.PRODUCE]: [],
    [ReasonDetailType.SYSTEM]: []
  }
  for (const detail of details) {
    detailRecord[detail.type].push(detail)
  }
  currentRow.value = {
    id,
    category,
    subCategory,
    improvement: improvement ?? undefined,
    unqualityType: unqualityType ?? undefined,
    unqualityCode: unqualityCode ?? undefined,
    estimatedFinishOn,
    details: detailRecord,
    ownerName: config?.ownerName,
    ownerDepartment: config?.ownerDepartment,
    validateResult: validateResult ?? undefined,
    state,
    stateIdx
  } as any
}
const visible = ref(false)
const currentRow = ref<Partial<ReasonDto> | null>(null)
const actionType = ref('')
const usersRef = ref<User[]>([])
const departmentsRef = ref<Department[]>([])
const convertStaff = (u: User | undefined) => {
  if (!!u) {
    const departments = unref(departmentsRef)
    return {
      id: u.id,
      name: u.name,
      department: departments.find((item) => item.id === u.deptId)?.name,
      email: u.email
    } as Staff
  }
  return undefined
}
const convertStaffFromConfig = ({
  ownerId,
  ownerName,
  ownerEmail,
  ownerDepartment
}: ReasonConfig) => {
  return { id: ownerId, name: ownerName, email: ownerEmail, department: ownerDepartment }
}
const convertConfigFromStaff = ({ id, name, department, email, state, stateIdx, copy }: any) => {
  return {
    ownerId: id,
    ownerName: name,
    ownerEmail: email,
    ownerDepartment: department,
    state,
    stateIdx,
    copy
  }
}
const action = async (row: Reason | null, type: string) => {
  if (type === 'del') {
    try {
      await delReasonApi(props.id!, [row!.id])
      refresh()
    } catch {}
  } else if (type === 'view') {
    actionType.value = type
    genFormData(row!)
    visible.value = true
  } else {
    actionType.value = type
    if (row === null) {
      const users = unref(usersRef)
      const validator = users.find((item) => item.id === props.problem?.cqeId)
      const creator = users.find((item) => item.id === Number(props.problem?.creatorId))
      const validatorAudit = users.find((item) => item.id === creator?.leader)
      const validatorCopy = users.find((item) => item.id === creator?.indirectLeader)
      const cqeAudit = users.find((item) => item.id === validator?.leader)
      currentRow.value = {
        validator: convertStaff(validator),
        validatorAudit: convertStaff(validatorAudit),
        validatorCopy: convertStaff(validatorCopy),
        cqeAudit: convertStaff(cqeAudit)
      }
    } else {
      const { id, configs, category, subCategory } = row
      currentRow.value = {
        id,
        category,
        subCategory,
        anlayzer: convertStaffFromConfig(configs.find((item) => item.state === NodeState.ANALYZE)!),
        anlayzerAudit: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.ANALYZE_AUDIT)!
        ),
        anlayzerCopy: convertStaffFromConfig(configs.find((item) => item.copy === 'ANALYZE')!),
        validator: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.VALIDATE)!
        ),
        validatorAudit: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.VALIDATE_AUDIT)!
        ),
        validatorCopy: convertStaffFromConfig(configs.find((item) => item.copy === 'VALIDATE')!),
        cqeAudit: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.CQE_AUDIT)!
        )
      }
    }
    visible.value = true
  }
}

const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const res = await getReasonListApi(props.id!)
    const { data } = res
    model.value = { valid: data.length > 0 }
    return {
      list: data,
      total: data.length
    }
  }
})
const { loading, dataList } = tableState
const { refresh } = tableMethods
const saveLoading = ref(false)
const save = async () => {
  const write = unref(formRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true
    const {
      id,
      category,
      subCategory,
      anlayzer,
      anlayzerAudit,
      anlayzerCopy,
      validator,
      validatorAudit,
      validatorCopy,
      cqeAudit
    } = formData
    try {
      await saveReasonApi(props.id!, {
        id,
        category,
        subCategory,
        configs: [
          convertConfigFromStaff({ ...anlayzer, state: NodeState.ANALYZE, stateIdx: 0 }),
          convertConfigFromStaff({ ...anlayzerAudit, state: NodeState.ANALYZE_AUDIT, stateIdx: 1 }),
          convertConfigFromStaff({ ...anlayzerCopy, copy: 'ANALYZE' }),
          convertConfigFromStaff({ ...validator, state: NodeState.VALIDATE, stateIdx: 2 }),
          convertConfigFromStaff({
            ...validatorAudit,
            state: NodeState.VALIDATE_AUDIT,
            stateIdx: 3
          }),
          convertConfigFromStaff({ ...validatorCopy, copy: 'VALIDATE' }),
          convertConfigFromStaff({ ...cqeAudit, state: NodeState.CQE_AUDIT, stateIdx: 4 })
        ]
      })
      visible.value = false
      refresh()
    } catch {
    } finally {
      saveLoading.value = false
    }
  }
}
const tableColumns: TableColumn[] = props.draft
  ? [
      {
        field: 'index',
        label: t('序号'),
        type: 'index'
      },
      {
        field: 'category',
        label: t('原因类别(一级)'),
        formatter: (_, __, val) => t(val)
      },
      {
        field: 'subCategory',
        label: t('原因类别(二级)'),
        formatter: (_, __, val) => t(val)
      },
      {
        field: 'status',
        label: t('状态')
      },
      {
        field: 'responsible',
        label: t('责任人'),
        formatter: (entity: Recordable, __: TableColumn) => {
          return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerName
        }
      },
      {
        field: 'responsibleDep',
        label: t('部门'),
        formatter: (entity: Recordable, __: TableColumn) => {
          return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerDepartment
        }
      },
      {
        field: 'action',
        label: t('操作'),
        width: 140,
        fixed: 'right',
        slots: {
          default: (data: { row: Reason }) => {
            return data.row.delete ? (
              <ElTag type="info">{t('已作废')}</ElTag>
            ) : (
              <>
                <BaseButton type="text" onClick={() => action(data.row, 'edit')}>
                  {t('编辑')}
                </BaseButton>
                <ElPopconfirm
                  title={t('是否确认删除')}
                  onConfirm={() => action(data.row, 'del')}
                  v-slots={{
                    reference: () => {
                      return (
                        <BaseButton type="danger" link>
                          {t('删除')}
                        </BaseButton>
                      )
                    }
                  }}
                />
              </>
            )
          }
        }
      }
    ]
  : [
      {
        field: 'index',
        label: t('序号'),
        type: 'index'
      },
      {
        field: 'category',
        label: t('原因类别(一级)'),
        width: 120,
        formatter: (_, __, val) => t(val)
      },
      {
        field: 'subCategory',
        label: t('原因类别(二级)'),
        width: 120,
        formatter: (_, __, val) => t(val)
      },
      {
        field: 'unqualityType',
        label: t('不良类别'),
        width: 120,
        formatter: (_, __, val) => t(val)
      },
      {
        field: 'unqualityCode',
        label: t('不良代码'),
        width: 120,
        formatter: (_, __, val) => t(val)
      },
      {
        field: 'improvement',
        label: t('行动计划'),
        width: 120,
        formatter: (_, __, val) => t(val)
      },
      {
        field: 'status',
        label: t('状态'),
        width: 120
      },
      {
        field: 'estimatedFinishOn',
        label: t('预计完成日期'),
        width: 120
      },
      {
        field: 'finishOn',
        label: t('实际完成日期'),
        width: 120
      },
      {
        field: 'responsible',
        label: t('责任人'),
        formatter: (entity: Recordable, __: TableColumn) => {
          return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerName
        }
      },
      {
        field: 'responsibleDep',
        label: t('部门'),
        formatter: (entity: Recordable, __: TableColumn) => {
          return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerDepartment
        }
      },
      {
        field: 'validateResult',
        label: t('验证结果')
      },
      {
        field: 'validateOn',
        label: t('验证日期')
      },
      {
        field: 'action',
        label: t('操作'),
        width: 140,
        fixed: 'right',
        slots: {
          default: (data: { row: Reason }) => {
            if (data.row.delete) {
              return <ElTag type="info">{t('已作废')}</ElTag>
            }
            if (props.readonly) {
              return (
                <BaseButton type="text" onClick={() => action(data.row, 'view')}>
                  {t('查看')}
                </BaseButton>
              )
            }
            return (
              <>
                <BaseButton type="text" onClick={() => action(data.row, 'view')}>
                  {t('查看')}
                </BaseButton>
                {/* <ElPopconfirm
                  title={t('是否确认作废')}
                  onConfirm={() => action(data.row, 'del')}
                  v-slots={{
                    reference: () => {
                      return (
                        <BaseButton type="danger" link>
                          {t('作废')}
                        </BaseButton>
                      )
                    }
                  }}
                /> */}
              </>
            )
          }
        }
      }
    ]

onMounted(async () => {
  const userRes = await getUsersApi()
  usersRef.value = userRes.data
  const departmentsRes = await getDepartmentsApi()
  departmentsRef.value = departmentsRes.data
})
const formRef = ref<ComponentRef<typeof ReasonForm>>()
</script>

<template>
  <ContentWrap>
    <div class="mb-10px" v-if="!readonly">
      <BaseButton type="primary" @click="action(null, 'add')">{{ t('新增') }}</BaseButton>
    </div>
    <Table
      align="center"
      headerAlign="center"
      :columns="tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      @refresh="refresh"
    />
    <Dialog
      v-model="visible"
      :fullscreen="false"
      :title="
        t(
          `${
            currentRow?.id
              ? actionType === 'view'
                ? '查看可疑因子'
                : '编辑可疑因子'
              : '新增可疑因子'
          }`
        )
      "
    >
      <ReasonDetailForm v-if="actionType === 'view'" :current-row="currentRow" />
      <ReasonForm
        v-else
        ref="formRef"
        :current-row="currentRow"
        :users="usersRef"
        :departments="departmentsRef"
      />
      <template #footer>
        <BaseButton
          v-if="actionType !== 'view'"
          type="primary"
          :loading="saveLoading"
          @click="save"
        >
          {{ t('保存') }}
        </BaseButton>
      </template>
    </Dialog>
  </ContentWrap>
</template>
