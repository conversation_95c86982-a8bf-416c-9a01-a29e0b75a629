import { Icon } from '@/components/Icon'
import { useI18n } from '@/hooks/web/useI18n'
import { useTodoStore } from '@/store/modules/todo'
import { ElBadge, ElTooltip } from 'element-plus'
import type { RouteMeta } from 'vue-router'

export const useRenderMenuTitle = () => {
  const renderMenuTitle = (meta: RouteMeta, name: string) => {
    const todoStore = useTodoStore()
    const { t } = useI18n()
    const { title = 'Please set title', icon } = meta
    let titleDom = (
      <ElTooltip effect="dark" placement="right" content={t(title as string)}>
        <span class="v-menu__title">{t(title as string)}</span>
      </ElTooltip>
    )
    if (name === 'Task' && todoStore.getTodoTask > 0) {
      titleDom = (
        <ElBadge value={todoStore.getTodoTask} max={99} class="h-[24px] leading-[24px]">
          <ElTooltip effect="dark" placement="right" content={t(title as string)}>
            <span class="v-menu__title inline">{t(title as string)}</span>
          </ElTooltip>
        </ElBadge>
      )
    } else if (name === 'Audit' && todoStore.getTodoAudit > 0) {
      titleDom = (
        <ElBadge value={todoStore.getTodoAudit} max={99} class="h-[24px] leading-[24px]">
          <ElTooltip effect="dark" placement="right" content={t(title as string)}>
            <span class="v-menu__title inline">{t(title as string)}</span>
          </ElTooltip>
        </ElBadge>
      )
    }
    return icon ? (
      <>
        <Icon icon={meta.icon}></Icon>
        {titleDom}
      </>
    ) : (
      titleDom
    )
  }

  return {
    renderMenuTitle
  }
}
