import { loginOutApi } from '@/api/login'
import { SysRouter, UserType } from '@/api/login/types'
import { useI18n } from '@/hooks/web/useI18n'
import router from '@/router'
import { useAppStore } from '@/store/modules/app'
import { ElMessageBox } from 'element-plus'
import { defineStore } from 'pinia'
import { store } from '../index'
import { useTagsViewStore } from './tagsView'
import { useTodoStore } from './todo'

interface UserState {
  userInfo?: UserType
  tokenKey: string
  token: string
  roleRouters?: string[] | AppCustomRouteRecordRaw[]
  sysRouters?: SysRouter[]
  permissions?: string[]
  redirect: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => {
    return {
      userInfo: undefined,
      tokenKey: 'Authorization',
      token: '',
      roleRouters: undefined,
      sysRouters: undefined,
      redirect: false,
      permissions: undefined
    }
  },
  getters: {
    getTokenKey(): string {
      return this.tokenKey
    },
    getToken(): string {
      return this.token
    },
    getUserInfo(): UserType | undefined {
      return this.userInfo
    },
    getRoleRouters(): string[] | AppCustomRouteRecordRaw[] | undefined {
      return this.roleRouters
    },
    getSysRouters(): SysRouter[] {
      return this.sysRouters ?? []
    },
    getPermissions(): string[] {
      return this.permissions ?? []
    },
    isRedirect(): boolean {
      return this.redirect
    },
    isAdmin(): boolean {
      return (this.userInfo?.roleId ?? []).includes('1')
    },
    getLang(): LocaleType {
      return this.userInfo?.lang === 'en' ? 'en' : 'zh-CN'
    }
  },
  actions: {
    setLang(code: string) {
      if (!!this.userInfo && this.userInfo.lang !== code) {
        this.userInfo.lang = code
      }
    },
    setTokenKey(tokenKey: string) {
      this.tokenKey = tokenKey
    },
    setToken(token: string) {
      this.token = token
    },
    setUserInfo(userInfo?: UserType) {
      this.userInfo = userInfo
    },
    setRoleRouters(roleRouters: string[] | AppCustomRouteRecordRaw[]) {
      this.roleRouters = roleRouters
    },
    setSysRouters(sysRouters: SysRouter[]) {
      this.sysRouters = sysRouters
    },
    setPermissions(permissions: string[]) {
      this.permissions = permissions
    },
    setRedirect(redirect: boolean) {
      this.redirect = redirect
    },
    logoutConfirm() {
      const { t } = useI18n()
      ElMessageBox.confirm(t('是否确认登出'), t('登出提醒'), {
        confirmButtonText: t('确定'),
        cancelButtonText: t('取消'),
        type: 'warning'
      })
        .then(async () => {
          this.reset()
        })
        .catch(() => {})
    },
    reset(skip: boolean = true) {
      const tagsViewStore = useTagsViewStore()
      const todoStore = useTodoStore()
      const appStore = useAppStore()
      if (skip) {
        loginOutApi().then(() => {
          tagsViewStore.delAllViews()
          this.setToken('')
          todoStore.setTodo({ task: 0, audit: 0 })
          appStore.setAuthKey('')
          this.setUserInfo(undefined)
          this.setRoleRouters([])
          this.setSysRouters([])
          this.setPermissions([])
          this.setRedirect(false)
          router.replace('/dashboard')
        })
      } else {
        tagsViewStore.delAllViews()
        this.setToken('')
        appStore.setAuthKey('')
        todoStore.setTodo({ task: 0, audit: 0 })
        this.setUserInfo(undefined)
        this.setRoleRouters([])
        this.setSysRouters([])
        this.setPermissions([])
        this.setRedirect(false)
      }
    },
    logout(skip: boolean = true) {
      this.reset(skip)
    }
  },
  persist: true
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
