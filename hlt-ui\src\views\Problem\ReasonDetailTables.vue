<script setup lang="tsx">
import { Attachment } from '@/api/base/types'
import { ReasonDetail, ReasonDetailOption, ReasonDetailType } from '@/api/problem/types'
import { Icon } from '@/components/Icon'
import { TableColumn } from '@/components/Table'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { ElLink, ElSpace } from 'element-plus'
const { t } = useI18n()
const emit = defineEmits(['change'])
const model = defineModel<ReasonDetail[]>({
  type: Object as PropType<ReasonDetail[]>,
  default: () => {}
})
const tableConfigs = ref<Record<ReasonDetailType, any>>({
  [ReasonDetailType.EXPOSE]: {},
  [ReasonDetailType.PRODUCE]: {},
  [ReasonDetailType.SYSTEM]: {}
})
const BASE_PATH = import.meta.env.VITE_API_BASE_PATH
const tableColumns = reactive<TableColumn[]>([
  {
    field: 'type',
    label: t('原因类别'),
    width: 100,
    formatter: (_, __, cellValue: string) => {
      return t(ReasonDetailOption[cellValue])
    }
  },
  {
    field: 'index',
    label: t('序号'),
    type: 'index'
  },
  {
    field: 'question',
    minWidth: 150,
    label: t('问题')
  },
  {
    field: 'answer',
    minWidth: 150,
    label: t('答案')
  },
  {
    field: 'evidence',
    minWidth: 150,
    label: t('证据')
  },
  {
    field: 'attachment',
    label: t('附件'),
    formatter: (row, __, cellValue: Attachment) => {
      return !!cellValue ? (
        <ElLink
          type="primary"
          href={`${BASE_PATH}/task/download/${row.id}`}
          {...{ target: '_blank' }}
        >
          <ElSpace>
            <Icon icon="ant-design:download-outlined" />
            {cellValue.filename}
          </ElSpace>
        </ElLink>
      ) : (
        <>-</>
      )
    }
  }
])

const { tableRegister, tableMethods } = useTable({
  fetchDataApi: async () => {
    const data = model.value
    return {
      list: data,
      total: data.length
    }
  }
})

watch(
  () => model.value,
  (val) => {
    for (const key of Object.keys(val ?? {})) {
      const { tableRegister, tableMethods, tableState } = useTable({
        fetchDataApi: async () => {
          const data = val[key]
          return {
            list: data,
            total: data.length
          }
        }
      })
      tableConfigs.value[key] = {
        tableRegister,
        tableMethods,
        tableState
      }
      tableConfigs.value[key].tableMethods.refresh()
    }
  },
  { deep: true, immediate: true }
)
</script>

<template>
  <Table
    align="center"
    headerAlign="center"
    :columns="tableColumns"
    :data="model"
    @register="tableRegister"
    @refresh="tableMethods.refresh"
  />
</template>
