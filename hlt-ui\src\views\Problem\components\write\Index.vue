<script setup lang="tsx">
import { getRelationsApi } from '@/api/dictionary'
import { DictionaryRelation } from '@/api/dictionary/types'
import { Problem } from '@/api/problem/types'
import { Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { useI18n } from '@/hooks/web/useI18n'
import { useValidator } from '@/hooks/web/useValidator'
import { isUnDef } from '@/utils/is'
import { propTypes } from '@/utils/propTypes'
import { ElTabPane, ElTabs } from 'element-plus'
import Log from './Log.vue'
import Reason from './Reason.vue'

const { t } = useI18n()

const { required, whitespace } = useValidator()

const props = defineProps({
  actionType: propTypes.string.def('edit'),
  draft: propTypes.bool.def(true),
  cqe: propTypes.bool.def(false),
  currentRow: {
    type: Object as PropType<Nullable<Partial<Problem>>>,
    default: () => null
  },
  formSchema: {
    type: Array as PropType<FormSchema[]>,
    default: () => []
  }
})
const rules = reactive(
  props.actionType === 'edit'
    ? {
        createdOn: [required()],
        // businessUnit: [required()],
        factory: [required()],
        customer: [required()],
        machineType: [required()],
        productStep: [required()],
        productLine: [required()],
        // projectCode: [required(), whitespace()],
        workOrderCode: [required()],
        'descriptions.what': [required()],
        'descriptions.when': [required()],
        'descriptions.why': [required()],
        'descriptions.where': [required()],
        'descriptions.who': [required()],
        'descriptions.how_many': [required()],
        'descriptions.how_detected': [required()],
        workOrderNum: [required()],
        // machineCategory: [required()],
        description: [required(), whitespace()],
        cqeId: [required()]
      }
    : {}
)
const activeTab = ref<string>('tab1')
const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose, getFieldValue } = formMethods

const reasonValid = ref({ valid: props.draft })
const relations = ref<Record<string, DictionaryRelation>>({})

onBeforeMount(async () => {
  const res = await getRelationsApi()
  relations.value = (res.data as DictionaryRelation[]).reduce((prev, next) => {
    prev[next.machineType] = next
    return prev
  }, {})
})
const submit = async (skipValidate: boolean = false) => {
  const elForm = await getElFormExpose()
  const valid =
    skipValidate ||
    (await elForm?.validate().catch((err) => {
      console.log(err)
    }))
  if (valid) {
    const isReasonValid = unref(reasonValid)
    if (skipValidate || isReasonValid.valid) {
      return await getFormData<Problem>()
    } else {
      ElMessage.error(t('请至少添加一条原因分析'))
      activeTab.value = 'tab2'
    }
  } else {
    activeTab.value = 'tab1'
  }
}

const schemas = computed(() => {
  const field = props.formSchema.find((item) => item.field === 'machineType')
  if (!!field) {
    field.componentProps = field.componentProps ?? {}
    const { onChange } = field.componentProps
    field.componentProps.onChange = async (val?: string) => {
      const _relations = unref(relations)
      if (!!val?.length && !!_relations[val]) {
        const relation = _relations[val]
        formMethods.setFieldValue('customer', relation?.customer)
        formMethods.setFieldValue('businessUnit', relation?.businessUnit)
      } else {
        formMethods.setFieldValue('customer', undefined)
        formMethods.setFieldValue('businessUnit', undefined)
      }
      onChange?.(val)
    }
  }
  const field2 = props.formSchema.find((item) => item.field === 'cqeId')
  if (!!field2) {
    Object.assign(field2, {
      rules: [
        {
          asyncValidator: (rule) => {
            return new Promise((resolve, reject) => {
              setTimeout(async () => {
                const val = await getFieldValue(rule.field!)
                if (isUnDef(val)) {
                  reject(t('该项为必填项'))
                } else {
                  resolve(true)
                }
              }, 100)
            })
          }
        }
      ]
    })
  }
  return props.formSchema
})
watch(
  () => props.currentRow,
  (currentRow) => {
    if (!currentRow) return
    setValues(currentRow)
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})
</script>

<template>
  <ElTabs v-model="activeTab">
    <ElTabPane :label="t('问题描述')" name="tab1">
      <Form
        labelPosition="top"
        :rules="rules"
        :schema="schemas"
        @register="formRegister"
        :disabled="actionType === 'view'"
      />
    </ElTabPane>
    <ElTabPane :label="t('原因分析')" name="tab2" v-if="!props.draft">
      <Reason
        v-model="reasonValid"
        :id="currentRow?.id"
        :problem="currentRow as Problem"
        :draft="draft || cqe"
        :readonly="actionType === 'view'"
      />
    </ElTabPane>
    <ElTabPane :label="t('流程明细')" name="tab3" lazy>
      <Log :id="currentRow?.id" />
    </ElTabPane>
  </ElTabs>
</template>
