import { defineStore } from 'pinia'
import { store } from '../index'

interface TodoState {
  task: number
  audit: number
}

export const useTodoStore = defineStore('todo', {
  state: (): TodoState => {
    return {
      task: 0,
      audit: 0
    }
  },
  getters: {
    getTodoTask(): number {
      return this.task
    },
    getTodoAudit(): number {
      return this.audit
    }
  },
  actions: {
    setTodo(todo: TodoState) {
      this.task = todo.task
      this.audit = todo.audit
    }
  },
  persist: false
})

export const useTodoStoreWithOut = () => {
  return useTodoStore(store)
}
