import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { useAppStore } from '@/store/modules/app'
import { isUrl } from '@/utils/is'
import { pathResolve } from '@/utils/routerHelper'
import { ElMenuItem, ElSubMenu, ElTooltip } from 'element-plus'
import { hasOneShowingChild } from '../helper'
import { useRenderMenuTitle } from './useRenderMenuTitle'

const { renderMenuTitle } = useRenderMenuTitle()

export const useRenderMenuItem = (
  // allRouters: AppRouteRecordRaw[] = [],
  menuMode: 'vertical' | 'horizontal'
) => {
  const renderMenuItem = (routers: AppRouteRecordRaw[], parentPath = '/', nested = false) => {
    const { t } = useI18n()
    const { getPrefixCls } = useDesign()
    const isCar = import.meta.env.VITE_APP_TITLE === 'CAR'
    const preFixCls = getPrefixCls('menu-popper')
    const appStore = useAppStore()
    const cur_menus = routers.map((v) => {
      const meta = v.meta ?? {}
      if (!meta.hidden) {
        const { oneShowingChild, onlyOneChild } = hasOneShowingChild(v.children, v)
        const fullPath = isUrl(v.path) ? v.path : pathResolve(parentPath, v.path) // getAllParentPath<AppRouteRecordRaw>(allRouters, v.path).join('/')
        if (
          oneShowingChild &&
          (!onlyOneChild?.children || onlyOneChild?.noShowingChildren) &&
          !meta?.alwaysShow
        ) {
          return (
            <ElMenuItem index={onlyOneChild ? pathResolve(fullPath, onlyOneChild.path) : fullPath}>
              {{
                default: () => renderMenuTitle(onlyOneChild ? onlyOneChild?.meta : meta, v.name)
              }}
            </ElMenuItem>
          )
        } else {
          return (
            <ElSubMenu
              index={fullPath}
              popperClass={
                menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
              }
            >
              {{
                title: () => renderMenuTitle(meta, v.name),
                default: () => renderMenuItem(v.children!, fullPath, true)
              }}
            </ElSubMenu>
          )
        }
      }
    })
    if (nested) {
      return cur_menus
    }
    return [
      <ElMenuItem index={'http://***********'}>
        <div
          class="v-menu__title inline truncate"
          style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
        >
          <ElTooltip effect="dark" placement="right" content={t('首页')}>
            <span class="w-full inline-block truncate">{t('首页')}</span>
          </ElTooltip>
        </div>
      </ElMenuItem>,
      <ElSubMenu
        index={'客户质量'}
        popperClass={
          menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
        }
      >
        {{
          title: () => (
            <div
              class="v-menu__title inline truncate"
              style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
            >
              <ElTooltip effect="dark" placement="right" content={t('客户质量')}>
                <span class="w-full inline-block truncate">{t('客户质量')}</span>
              </ElTooltip>
            </div>
          ),
          default: () => [
            <ElMenuItem index={'http://***********:8502'}>
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('8D')}>
                  <span class="w-full inline-block truncate">{t('8D')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem index={'http://***********:8501'}>
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('客户评价')}>
                  <span class="w-full inline-block truncate">{t('客户评价')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('客户审核')}>
                  <span class="w-full inline-block truncate">{t('客户审核')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('客户投诉管理')}>
                  <span class="w-full inline-block truncate">{t('客户投诉管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('退换货管理')}>
                  <span class="w-full inline-block truncate">{t('退换货管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('顾客满意度管理')}>
                  <span class="w-full inline-block truncate">{t('顾客满意度管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>
          ]
        }}
      </ElSubMenu>,
      <ElSubMenu
        index={'新产品开发质量'}
        popperClass={
          menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
        }
      >
        {{
          title: () => (
            <div
              class="v-menu__title inline truncate"
              style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
            >
              <ElTooltip effect="dark" placement="right" content={t('新产品开发质量')}>
                <span class="w-full inline-block truncate">{t('新产品开发质量')}</span>
              </ElTooltip>
            </div>
          ),
          default: () => [
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('设计质量管理')}>
                  <span class="w-full inline-block truncate">{t('设计质量管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            !isCar ? (
              <ElSubMenu
                index={'DFX'}
                popperClass={
                  menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
                }
              >
                {{
                  title: () => (
                    <div
                      class="v-menu__title inline truncate"
                      style={
                        appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}
                      }
                    >
                      <ElTooltip effect="dark" placement="right" content={t('DFX')}>
                        <span class="w-full inline-block truncate">{t('DFX')}</span>
                      </ElTooltip>
                    </div>
                  ),
                  default: () => cur_menus
                }}
              </ElSubMenu>
            ) : (
              <ElMenuItem index={'http://***********:8504'}>
                <div
                  class="v-menu__title inline truncate"
                  style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
                >
                  <ElTooltip effect="dark" placement="right" content={t('DFX')}>
                    <span class="w-full inline-block truncate">{t('DFX')}</span>
                  </ElTooltip>
                </div>
              </ElMenuItem>
            )
          ]
        }}
      </ElSubMenu>,
      <ElSubMenu
        index={'生产过程质量'}
        popperClass={
          menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
        }
      >
        {{
          title: () => (
            <div
              class="v-menu__title inline truncate"
              style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
            >
              <ElTooltip effect="dark" placement="right" content={t('生产过程质量')}>
                <span class="w-full inline-block truncate">{t('生产过程质量')}</span>
              </ElTooltip>
            </div>
          ),
          default: () => [
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('首件鉴定管理')}>
                  <span class="w-full inline-block truncate">{t('首件鉴定管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('PQC')}>
                  <span class="w-full inline-block truncate">{t('PQC')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('FQC')}>
                  <span class="w-full inline-block truncate">{t('FQC')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('OBA')}>
                  <span class="w-full inline-block truncate">{t('OBA')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('OQC')}>
                  <span class="w-full inline-block truncate">{t('OQC')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('关键工序SPC控制')}>
                  <span class="w-full inline-block truncate">{t('关键工序SPC控制')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('不合格品审理')}>
                  <span class="w-full inline-block truncate">{t('不合格品审理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            isCar ? (
              <ElSubMenu
                index={'CAR'}
                popperClass={
                  menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
                }
              >
                {{
                  title: () => (
                    <div
                      class="v-menu__title inline truncate"
                      style={
                        appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}
                      }
                    >
                      <ElTooltip effect="dark" placement="right" content={t('CAR')}>
                        <span class="w-full inline-block truncate">{t('CAR')}</span>
                      </ElTooltip>
                    </div>
                  ),
                  default: () => cur_menus
                }}
              </ElSubMenu>
            ) : (
              <ElMenuItem index={'http://***********:8503'}>
                <div
                  class="v-menu__title inline truncate"
                  style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
                >
                  <ElTooltip effect="dark" placement="right" content={t('CAR')}>
                    <span class="w-full inline-block truncate">{t('CAR')}</span>
                  </ElTooltip>
                </div>
              </ElMenuItem>
            ),
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('变更管理')}>
                  <span class="w-full inline-block truncate">{t('变更管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('抽检规则')}>
                  <span class="w-full inline-block truncate">{t('抽检规则')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>
          ]
        }}
      </ElSubMenu>,
      <ElSubMenu
        index={'供方质量'}
        popperClass={
          menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
        }
      >
        {{
          title: () => (
            <div
              class="v-menu__title inline truncate"
              style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
            >
              <ElTooltip effect="dark" placement="right" content={t('供方质量')}>
                <span class="w-full inline-block truncate">{t('供方质量')}</span>
              </ElTooltip>
            </div>
          ),
          default: () => [
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('供方质量管理')}>
                  <span class="w-full inline-block truncate">{t('供方质量管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('来料检验管理')}>
                  <span class="w-full inline-block truncate">{t('来料检验管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>
          ]
        }}
      </ElSubMenu>,
      <ElSubMenu
        index={'质量体系'}
        popperClass={
          menuMode === 'vertical' ? `${preFixCls}--vertical` : `${preFixCls}--horizontal`
        }
      >
        {{
          title: () => (
            <div
              class="v-menu__title inline truncate"
              style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
            >
              <ElTooltip effect="dark" placement="right" content={t('质量体系')}>
                <span class="w-full inline-block truncate">{t('质量体系')}</span>
              </ElTooltip>
            </div>
          ),
          default: () => [
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('持续改进管理')}>
                  <span class="w-full inline-block truncate">{t('持续改进管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('知识管理')}>
                  <span class="w-full inline-block truncate">{t('知识管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('质量风险管理')}>
                  <span class="w-full inline-block truncate">{t('质量风险管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('质量专项工作管理')}>
                  <span class="w-full inline-block truncate">{t('质量专项工作管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>,
            <ElMenuItem
              onClick={() => {
                ElMessage.warning(t('模块开发中'))
              }}
            >
              <div
                class="v-menu__title inline truncate"
                style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
              >
                <ElTooltip effect="dark" placement="right" content={t('测量设备管理')}>
                  <span class="w-full inline-block truncate">{t('测量设备管理')}</span>
                </ElTooltip>
              </div>
            </ElMenuItem>
          ]
        }}
      </ElSubMenu>,
      <ElMenuItem
        onClick={() => {
          ElMessage.warning(t('模块开发中'))
        }}
      >
        <div
          class="v-menu__title inline truncate"
          style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
        >
          <ElTooltip effect="dark" placement="right" content={t('实验室管理')}>
            <span class="w-full inline-block truncate">{t('实验室管理')}</span>
          </ElTooltip>
        </div>
      </ElMenuItem>,
      <ElMenuItem index={'http://***********/#/list'}>
        <div
          class="v-menu__title inline truncate"
          style={appStore.getCollapse ? { marginLeft: '-10px', marginRight: '-10px' } : {}}
        >
          <ElTooltip effect="dark" placement="right" content={t('质量之声')}>
            <span class="w-full inline-block truncate">{t('质量之声')}</span>
          </ElTooltip>
        </div>
      </ElMenuItem>
    ]
  }

  return {
    renderMenuItem
  }
}
