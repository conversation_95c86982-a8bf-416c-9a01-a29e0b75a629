<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import dayjs from 'dayjs'
import { PropType } from 'vue'

export type ReasonDetailDto = {
  id: ID
  category: string
  subCategory: string
  ownerName: string
  ownerDepartment: string
  details: Recordable
  improvement: string
  unqualityType: string
  unqualityCode: string
  estimatedFinishOn: dayjs.Dayjs | string
}

const props = defineProps({
  actionType: {
    type: String as PropType<string>,
    default: () => 'edit'
  },
  currentRow: {
    type: Object as PropType<Nullable<Partial<ReasonDetailDto>>>,
    default: () => {}
  },
  schemas: {
    type: Array as PropType<FormSchema[]>,
    default: () => []
  }
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose } = formMethods

const submit = async (skipValidate: boolean = false) => {
  const elForm = await getElFormExpose()
  const valid =
    skipValidate ||
    (await elForm?.validate().catch((err) => {
      console.log(err)
    }))
  if (valid) {
    const formData = await getFormData<ReasonDetailDto>()
    return formData
  }
}

watch(
  () => props.currentRow,
  (curr) => {
    if (!curr) return
    setValues(curr)
  },
  {
    deep: true,
    immediate: true
  }
)
defineExpose({
  submit
})
</script>

<template>
  <Form labelPosition="top" :schema="schemas" @register="formRegister" />
</template>
