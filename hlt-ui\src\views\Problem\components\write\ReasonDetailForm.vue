<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { ComponentNameEnum, Form, FormSchema } from '@/components/Form'
import { ReasonDetailItem } from '@/components/Form/src/components/ReasonDetail'
import { useForm } from '@/hooks/web/useForm'
import { useI18n } from '@/hooks/web/useI18n'
import dayjs from 'dayjs'
import { PropType } from 'vue'

const { t } = useI18n()
export type ReasonDetailDto = {
  id: ID
  category: string
  subCategory: string
  ownerName: string
  ownerDepartment: string
  details: Recordable
  improvement: string
  unqualityType: string
  unqualityCode: string
  estimatedFinishOn: dayjs.Dayjs | string
  validateResult: string
}

const props = defineProps({
  actionType: {
    type: String as PropType<string>,
    default: () => 'edit'
  },
  currentRow: {
    type: Object as PropType<Nullable<Partial<ReasonDetailDto>>>,
    default: () => {}
  }
})

const schemas = ref<FormSchema[]>([
  {
    field: 'category',
    label: t('原因类别(一级)'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { span: 6 }
  },
  {
    field: 'subCategory',
    label: t('原因类别(二级)'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { span: 6 }
  },
  {
    field: 'ownerName',
    label: t('原因负责人'),
    component: ComponentNameEnum.TEXT,
    colProps: { span: 6 }
  },
  {
    field: 'ownerDepartment',
    label: t('部门'),
    component: ComponentNameEnum.TEXT,
    colProps: { span: 6 }
  },
  {
    field: 'details',
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <ReasonDetailItem v-model={formValue.details} entityId={formValue.id} readonly={true} />
          )
        }
      }
    }
  },
  {
    field: 'improvement',
    label: t('行动计划'),
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.improvement}
            </p>
          )
        }
      }
    }
  },
  {
    field: 'unqualityType',
    label: t('不良类别'),
    colProps: { span: 6 },
    component: ComponentNameEnum.TEXT
  },
  {
    field: 'unqualityCode',
    label: t('不良代码'),
    colProps: { span: 6 },
    component: ComponentNameEnum.TEXT
  },
  {
    field: 'estimatedFinishOn',
    label: t('预计完成日期'),
    colProps: { span: 6 },
    component: ComponentNameEnum.TEXT
  },
  {
    field: 'validateResult',
    label: t('验证结果'),
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
              {formValue.validateResult}
            </p>
          )
        }
      }
    }
  }
])
const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose } = formMethods

const submit = async (skipValidate: boolean = false) => {
  const elForm = await getElFormExpose()
  const valid =
    skipValidate ||
    (await elForm?.validate().catch((err) => {
      console.log(err)
    }))
  if (valid) {
    const formData = await getFormData<ReasonDetailDto>()
    return formData
  }
}

watch(
  () => props.currentRow,
  (curr) => {
    if (!curr) return
    setValues(curr)
  },
  {
    deep: true,
    immediate: true
  }
)
defineExpose({
  submit
})
</script>

<template>
  <Form labelPosition="top" :schema="schemas" @register="formRegister" />
</template>
