<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import dayjs from 'dayjs'
import { ElDatePicker, datePickerProps } from 'element-plus'

const emit = defineEmits(['update:modelValue'])
const props = defineProps(datePickerProps)
const setValues = (data: Recordable) => {
  emit('update:modelValue', data)
}
const { t } = useI18n()

const formModel = ref<(string | number | Date)[]>(
  Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue, props.modelValue]
)
watch(
  () => props.modelValue,
  (val) => {
    if (val === unref(formModel)) return
    formModel.value = Array.isArray(val) ? val : [val, val]
  }
)
</script>

<template>
  <div class="w-full flex gap-1">
    <ElDatePicker
      v-model="formModel[0]"
      :valueFormat="props.valueFormat ?? 'YYYY-MM-DD'"
      :placeholder="t('开始日期')"
      :disabledDate="
        (d) => {
          if (!!formModel[1]) {
            return dayjs(formModel[1]).isBefore(dayjs(d))
          }
          return false
        }
      "
      :type="props.type ?? 'date'"
      :format="props.format ?? 'YYYY-MM-DD'"
      @change="
        (val) => {
          setValues([val, formModel[1]])
        }
      "
    />
    <span>-</span>
    <ElDatePicker
      v-model="formModel[1]"
      :valueFormat="props.valueFormat ?? 'YYYY-MM-DD'"
      :placeholder="t('结束日期')"
      :disabledDate="
        (d) => {
          if (!!formModel[0]) {
            return dayjs(formModel[0]).isAfter(dayjs(d))
          }
          return false
        }
      "
      :type="props.type ?? 'date'"
      :format="props.format ?? 'YYYY-MM-DD'"
      @change="
        (val) => {
          setValues([formModel[0], val])
        }
      "
    />
  </div>
</template>
