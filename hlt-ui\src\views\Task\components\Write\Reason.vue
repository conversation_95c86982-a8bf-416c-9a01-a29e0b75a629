<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { getOptionByCategoryApi } from '@/api/dictionary'
import { DictionaryCategory } from '@/api/dictionary/types'
import { delReasonApi } from '@/api/problem'
import {
  NodeState,
  Reason,
  ReasonConfig,
  ReasonDetail,
  ReasonDetailType
} from '@/api/problem/types'
import { getReasonListApi, submitApi, transferReasonApi, updateReasonApi } from '@/api/task'
import { getDepartmentsApi, getUsersApi } from '@/api/user'
import { Department, User } from '@/api/user/types'
import { BaseButton } from '@/components/Button'
import { DescriptionsSchema } from '@/components/Descriptions'
import { ComponentNameEnum, FormSchema } from '@/components/Form'
import { ReasonDetailItem } from '@/components/Form/src/components/ReasonDetail'
import { Table, TableColumn } from '@/components/Table'
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { useValidator } from '@/hooks/web/useValidator'
import { useUserStore } from '@/store/modules/user'
import { propTypes } from '@/utils/propTypes'
import { ElMessage, ElTag } from 'element-plus'
import ReasonForm from './ReasonForm.vue'
import ReasonTransferForm, { ReasonDto } from './ReasonTransferForm.vue'

const { t } = useI18n()
const { getPrefixCls } = useDesign()

const dialogVisible = ref(false)
const prefixCls = getPrefixCls('log')
const userStore = useUserStore()
const props = defineProps({
  readonly: propTypes.bool.def(false),
  id: String as PropType<ID>
})

const currentRow = ref<Reason>()
const genFormData = (row: Reason) => {
  const {
    id,
    configs,
    details,
    category,
    subCategory,
    improvement,
    unqualityType,
    unqualityCode,
    estimatedFinishOn,
    validateResult,
    state,
    stateIdx
  } = row
  const config = configs.find((item) => item.state === NodeState.ANALYZE)
  const detailRecord: Record<ReasonDetailType, ReasonDetail[]> = {
    [ReasonDetailType.EXPOSE]: [],
    [ReasonDetailType.PRODUCE]: [],
    [ReasonDetailType.SYSTEM]: []
  }
  for (const detail of details) {
    detailRecord[detail.type].push(detail)
  }
  currentRow.value = {
    id,
    category,
    subCategory,
    improvement: improvement ?? undefined,
    unqualityType: unqualityType ?? undefined,
    unqualityCode: unqualityCode ?? undefined,
    estimatedFinishOn,
    details: detailRecord,
    ownerName: config?.ownerName,
    ownerDepartment: config?.ownerDepartment,
    validateResult: validateResult ?? undefined,
    state,
    stateIdx
  } as any
}

const visible = ref(false)
const transferRow = ref<Partial<ReasonDto> | null>(null)
const convertStaffFromConfig = ({
  ownerId,
  ownerName,
  ownerEmail,
  ownerDepartment
}: ReasonConfig) => {
  return { id: ownerId, name: ownerName, email: ownerEmail, department: ownerDepartment }
}
const convertConfigFromStaff = ({ id, name, department, email, state, stateIdx, copy }: any) => {
  return {
    ownerId: id,
    ownerName: name,
    ownerEmail: email,
    ownerDepartment: department,
    state,
    stateIdx,
    copy
  }
}
const actionType = ref('')
const usersRef = ref<User[]>([])
const departmentsRef = ref<Department[]>([])
const action = async (row: Reason, type: string) => {
  if (type === 'del') {
    try {
      await delReasonApi(props.id!, [row!.id])
      refresh()
    } catch {}
  } else if (type === 'transfer') {
    const { id, configs } = row
    if (row.state === NodeState.ANALYZE) {
      transferRow.value = {
        id,
        state: row.state,
        anlayzer: convertStaffFromConfig(configs.find((item) => item.state === NodeState.ANALYZE)!),
        anlayzerAudit: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.ANALYZE_AUDIT)!
        ),
        anlayzerCopy: convertStaffFromConfig(configs.find((item) => item.copy === 'ANALYZE')!)
      }
    } else {
      transferRow.value = {
        id,
        state: row.state,
        validator: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.VALIDATE)!
        ),
        validatorAudit: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.VALIDATE_AUDIT)!
        ),
        validatorCopy: convertStaffFromConfig(configs.find((item) => item.copy === 'VALIDATE')!),
        cqeAudit: convertStaffFromConfig(
          configs.find((item) => item.state === NodeState.CQE_AUDIT)!
        )
      }
    }
    visible.value = true
  } else {
    actionType.value = type
    genFormData(row)
    dialogVisible.value = true
  }
}
watch(
  () => dialogVisible.value,
  (visible) => {
    if (!visible) {
      actionType.value = ''
      currentRow.value = undefined
    }
  },
  { immediate: true }
)
const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const res = await getReasonListApi(props.id!)
    const { data } = res
    const _data = data.filter(
      (item) =>
        (item.state === NodeState.ANALYZE || item.state === NodeState.VALIDATE) &&
        item.configs.find(
          (config) => config.stateIdx === item.stateIdx && config.ownerId === userStore.userInfo?.id
        )
    )
    if (!!currentRow.value) {
      const row = _data.find((item) => item.id === currentRow.value?.id)
      if (!!row) {
        genFormData(row)
      }
    }
    return {
      list: _data,
      total: _data.length
    }
  }
})
const { loading, dataList } = tableState
const { refresh } = tableMethods
const saveLoading = ref(false)
const save = async (skipValidate: boolean = false) => {
  const write = unref(formRef)
  const formData = await write?.submit(skipValidate)
  if (formData) {
    const value = {
      id: formData.id,
      improvement: formData.improvement,
      unqualityType: formData.unqualityType,
      unqualityCode: formData.unqualityCode,
      estimatedFinishOn: formData.estimatedFinishOn,
      validateResult: formData.validateResult
    }
    if (skipValidate) {
      try {
        await updateReasonApi(formData.id, value)
        refresh()
      } catch {}
    } else {
      saveLoading.value = true
      try {
        let res = await updateReasonApi(formData.id, value)
        if (res) {
          dialogVisible.value = false
          refresh()
        }
      } catch {
        ElMessage.error(t('保存失败'))
      } finally {
        saveLoading.value = false
      }
    }
  }
}
const transfer = async () => {
  const write = unref(transferFormRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true
    const {
      id,
      state,
      anlayzer,
      anlayzerAudit,
      anlayzerCopy,
      validator,
      validatorAudit,
      validatorCopy,
      cqeAudit
    } = formData
    try {
      await transferReasonApi(props.id!, {
        id,
        configs:
          state === NodeState.ANALYZE
            ? [
                convertConfigFromStaff({ ...anlayzer, state: NodeState.ANALYZE, stateIdx: 0 }),
                convertConfigFromStaff({
                  ...anlayzerAudit,
                  state: NodeState.ANALYZE_AUDIT,
                  stateIdx: 1
                }),
                convertConfigFromStaff({ ...anlayzerCopy, copy: 'ANALYZE' })
              ]
            : [
                convertConfigFromStaff({ ...validator, state: NodeState.VALIDATE, stateIdx: 2 }),
                convertConfigFromStaff({
                  ...validatorAudit,
                  state: NodeState.VALIDATE_AUDIT,
                  stateIdx: 3
                }),
                convertConfigFromStaff({ ...validatorCopy, copy: 'VALIDATE' }),
                convertConfigFromStaff({ ...cqeAudit, state: NodeState.CQE_AUDIT, stateIdx: 4 })
              ]
      })
      visible.value = false
      refresh()
    } catch {
    } finally {
      saveLoading.value = false
    }
  }
}
const readonly = ref(props.readonly)
const tableColumns: TableColumn[] = [
  {
    field: 'index',
    label: t('序号'),
    type: 'index'
  },
  {
    field: 'category',
    label: t('原因类别(一级)'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'subCategory',
    label: t('原因类别(二级)'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'unqualityType',
    label: t('不良类别'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'unqualityCode',
    label: t('不良代码'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'improvement',
    label: t('行动计划'),
    width: 120
  },
  {
    field: 'status',
    label: t('状态'),
    width: 120
  },
  {
    field: 'estimatedFinishOn',
    label: t('预计完成日期'),
    width: 120
  },
  {
    field: 'finishOn',
    label: t('实际完成日期'),
    width: 120
  },
  {
    field: 'responsible',
    label: t('责任人'),
    formatter: (entity: Recordable, __: TableColumn) => {
      return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerName
    }
  },
  {
    field: 'responsibleDep',
    label: t('部门'),
    formatter: (entity: Recordable, __: TableColumn) => {
      return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerDepartment
    }
  },
  {
    field: 'validateResult',
    label: t('验证结果')
  },
  {
    field: 'validateOn',
    label: t('验证日期')
  },
  {
    field: 'action',
    label: t('操作'),
    width: 200,
    fixed: 'left',
    slots: {
      default: (data: { row: Reason }) => {
        if (data.row.delete) {
          return <ElTag type="info">{t('已作废')}</ElTag>
        }
        const statusIdxes = data.row.configs
          .filter((config) => config.ownerId === userStore.userInfo?.id)
          .map((config) => config.stateIdx ?? 0)
        const canProcess = statusIdxes.includes(data.row.stateIdx)
        return (
          <>
            <BaseButton type="text" onClick={() => action(data.row, 'view')}>
              {t('查看')}
            </BaseButton>
            {data.row.state === NodeState.VALIDATE && (
              <BaseButton
                disabled={!canProcess}
                type="text"
                onClick={() => action(data.row, 'validate')}
              >
                {t('验证')}
              </BaseButton>
            )}
            {data.row.state === NodeState.ANALYZE && (
              <BaseButton
                type="text"
                disabled={!canProcess}
                onClick={() => action(data.row, 'edit')}
              >
                {t('处理')}
              </BaseButton>
            )}
            {(data.row.state === NodeState.ANALYZE || data.row.state === NodeState.VALIDATE) && (
              <BaseButton
                type="text"
                disabled={!canProcess}
                onClick={() => action(data.row, 'transfer')}
              >
                {t('转办')}
              </BaseButton>
            )}
          </>
        )
      }
    }
  }
]
const formSchemas = ref<FormSchema[]>([
  {
    field: 'category',
    label: t('原因类别(一级)'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { span: 6 }
  },
  {
    field: 'subCategory',
    label: t('原因类别(二级)'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { span: 6 }
  },
  {
    field: 'ownerName',
    label: t('原因负责人'),
    component: ComponentNameEnum.TEXT,
    colProps: { span: 6 }
  },
  {
    field: 'ownerDepartment',
    label: t('部门'),
    component: ComponentNameEnum.TEXT,
    colProps: { span: 6 }
  },
  {
    field: 'details',
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <ReasonDetailItem
              v-model={formValue.details}
              entityId={formValue.id}
              readonly={readonly.value || formValue.state === NodeState.VALIDATE}
              onChange={() => {
                refresh()
              }}
            />
          )
        }
      }
    }
  },
  {
    field: 'improvement',
    label: t('行动计划'),
    colProps: { span: 24 },
    component: ComponentNameEnum.INPUT,
    componentProps: {
      type: 'textarea',
      showWordLimit: true,
      maxlength: 2000,
      autosize: { minRows: 6, maxRows: 10 },
      onBlur: async (e) => {
        if (!!e.target.value?.length) {
          await save(true)
        }
      }
    },
    formItemProps: {
      slots: readonly.value
        ? {
            default: ({ formValue }: any) => {
              return (
                <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                  {formValue.improvement}
                </p>
              )
            }
          }
        : undefined
    }
  },
  {
    field: 'unqualityType',
    label: t('不良类别'),
    colProps: { span: 6 },
    component: readonly.value ? ComponentNameEnum.TEXT : ComponentNameEnum.SELECT,
    componentProps: readonly.value
      ? { i18n: true }
      : {
          filterable: true,
          onChange: async (val) => {
            if (!!val?.length) {
              await save(true)
            }
          }
        },
    optionApi: async () => {
      const { data } = await getOptionByCategoryApi(DictionaryCategory.UNQUALITY_TYPE)
      return data.map((item) => ({
        label: t(item.name),
        value: item.name
      }))
    }
  },
  {
    field: 'unqualityCode',
    label: t('不良代码'),
    colProps: { span: 6 },
    component: readonly.value ? ComponentNameEnum.TEXT : ComponentNameEnum.SELECT,
    componentProps: readonly.value
      ? { i18n: true }
      : {
          filterable: true,
          onChange: async (val) => {
            if (!!val?.length) {
              await save(true)
            }
          }
        },
    optionApi: async () => {
      const { data } = await getOptionByCategoryApi(DictionaryCategory.UNQUALITY_CODE)
      return data.map((item) => ({
        label: t(item.name),
        value: item.name
      }))
    }
  },
  {
    field: 'estimatedFinishOn',
    label: t('预计完成日期'),
    colProps: { span: 6 },
    component: readonly.value ? ComponentNameEnum.TEXT : ComponentNameEnum.DATE_PICKER,
    componentProps: readonly.value
      ? undefined
      : {
          valueFormat: 'YYYY-MM-DD',
          onChange: async (val) => {
            if (!!val?.length) {
              await save(true)
            }
          }
        }
  }
])
onMounted(async () => {
  const userRes = await getUsersApi()
  usersRef.value = userRes.data
  const departmentsRes = await getDepartmentsApi()
  departmentsRef.value = departmentsRes.data
})
const isValidate = ref(false)
const { required, whitespace } = useValidator()
watch(
  () => ({
    row: currentRow.value,
    actionType: actionType.value
  }),
  ({ row, actionType }) => {
    readonly.value = row?.state === NodeState.VALIDATE || actionType === 'view'
    isValidate.value = row?.state === NodeState.VALIDATE
    const _schemas: FormSchema[] = [
      {
        field: 'category',
        label: t('原因类别(一级)'),
        component: ComponentNameEnum.TEXT,
        componentProps: { i18n: true },
        colProps: { span: 6 }
      },
      {
        field: 'subCategory',
        label: t('原因类别(二级)'),
        component: ComponentNameEnum.TEXT,
        componentProps: { i18n: true },
        colProps: { span: 6 }
      },
      {
        field: 'ownerName',
        label: t('原因负责人'),
        component: ComponentNameEnum.TEXT,
        colProps: { span: 6 }
      },
      {
        field: 'ownerDepartment',
        label: t('部门'),
        component: ComponentNameEnum.TEXT,
        colProps: { span: 6 }
      },
      {
        field: 'details',
        colProps: { span: 24 },
        formItemProps: {
          slots: {
            default: ({ formValue }: any) => {
              return (
                <ReasonDetailItem
                  v-model={formValue.details}
                  entityId={formValue.id}
                  readonly={readonly.value || formValue.state === NodeState.VALIDATE}
                  onChange={() => {
                    refresh()
                  }}
                />
              )
            }
          }
        }
      },
      {
        field: 'improvement',
        label: t('行动计划'),
        colProps: { span: 24 },
        component: ComponentNameEnum.INPUT,
        componentProps: {
          type: 'textarea',
          showWordLimit: true,
          maxlength: 2000,
          autosize: { minRows: 6, maxRows: 10 },
          onBlur: async (e) => {
            if (!!e.target.value?.length) {
              await save(true)
            }
          }
        },
        formItemProps: {
          slots: readonly.value
            ? {
                default: ({ formValue }: any) => {
                  return (
                    <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                      {formValue.improvement}
                    </p>
                  )
                }
              }
            : undefined
        }
      },
      {
        field: 'unqualityType',
        label: t('不良类别'),
        colProps: { span: 6 },
        component: readonly.value ? ComponentNameEnum.TEXT : ComponentNameEnum.SELECT,
        componentProps: readonly.value
          ? { i18n: true }
          : {
              filterable: true,
              onChange: async (val) => {
                if (!!val?.length) {
                  await save(true)
                }
              }
            },
        optionApi: async () => {
          const { data } = await getOptionByCategoryApi(DictionaryCategory.UNQUALITY_TYPE)
          return data.map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      {
        field: 'unqualityCode',
        label: t('不良代码'),
        colProps: { span: 6 },
        component: readonly.value ? ComponentNameEnum.TEXT : ComponentNameEnum.SELECT,
        componentProps: readonly.value
          ? { i18n: true }
          : {
              filterable: true,
              onChange: async (val) => {
                if (!!val?.length) {
                  await save(true)
                }
              }
            },
        optionApi: async () => {
          const { data } = await getOptionByCategoryApi(DictionaryCategory.UNQUALITY_CODE)
          return data.map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      {
        field: 'estimatedFinishOn',
        label: t('预计完成日期'),
        colProps: { span: 6 },
        component: readonly.value ? ComponentNameEnum.TEXT : ComponentNameEnum.DATE_PICKER,
        componentProps: readonly.value
          ? undefined
          : {
              valueFormat: 'YYYY-MM-DD',
              onChange: async (val) => {
                if (!!val?.length) {
                  await save(true)
                }
              }
            }
      }
    ]
    if (isValidate.value) {
      _schemas.push({
        field: 'validateResult',
        label: t('效果验证'),
        colProps: { span: 24 },
        component: ComponentNameEnum.INPUT,
        formItemProps: { rules: [required(), whitespace()] },
        componentProps: {
          type: 'textarea',
          showWordLimit: true,
          maxlength: 2000,
          placeholder: `${t('检查')}XXX, ${t('确认符合')}XXX
${t('实际测试')}XX, ${t('满足')}XX`,
          autosize: { minRows: 6, maxRows: 10 },
          onBlur: async (e) => {
            if (!!e.target.value?.length) {
              await save(true)
            }
          }
        }
      })
    } else if ((row?.stateIdx ?? 0) > 2) {
      _schemas.push({
        field: 'validateResult',
        label: t('效果验证'),
        colProps: { span: 24 },
        formItemProps: {
          slots: readonly.value
            ? {
                default: ({ formValue }: any) => {
                  return (
                    <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                      {formValue.validateResult}
                    </p>
                  )
                }
              }
            : undefined
        }
      })
    }
    formSchemas.value = _schemas
  },
  { immediate: true }
)
const formRef = ref<ComponentRef<typeof ReasonForm>>()
const transferFormRef = ref<ComponentRef<typeof ReasonTransferForm>>()

const schemas: DescriptionsSchema[] = reactive([
  {
    field: 'reasons',
    span: 24,
    slots: {
      default: (data) => {
        return (
          <Table
            align="center"
            headerAlign="center"
            columns={tableColumns}
            data={data}
            loading={loading.value}
            onRegister={tableRegister}
            onRefresh={refresh}
          />
        )
      }
    }
  }
])

const submit = async () => {
  const write = unref(formRef)
  const formData = await write?.submit()
  if (formData) {
    const { EXPOSE, PRODUCE } = currentRow.value?.details ?? {}
    if (!EXPOSE?.length) {
      ElMessage.error(t('流出原因不能为空'))
      return
    }
    if (!PRODUCE?.length) {
      ElMessage.error(t('产生原因不能为空'))
      return
    }
    const value = {
      id: formData.id,
      improvement: formData.improvement,
      unqualityType: formData.unqualityType,
      unqualityCode: formData.unqualityCode,
      estimatedFinishOn: formData.estimatedFinishOn
    }
    saveLoading.value = true
    try {
      let res = await updateReasonApi(formData.id, value)
      if (res) {
        ElMessageBox.confirm(t('是否确认提交'), t('确认操作'), {
          confirmButtonText: t('确认'),
          cancelButtonText: t('取消'),
          type: 'warning'
        }).then(
          async () => {
            try {
              const res = await submitApi(currentRow.value!.id!)
              if (res) {
                ElMessage.success(t('提交完成'))
                refresh()
                dialogVisible.value = false
              }
            } catch {}
          },
          (r) => {
            console.log(r)
          }
        )
      }
    } catch {
      ElMessage.error(t('保存失败'))
    } finally {
      saveLoading.value = false
    }
  }
}
const approve = async () => {
  const write = unref(formRef)
  const formData = await write?.submit()
  if (formData) {
    const value = {
      id: formData.id,
      validateResult: formData.validateResult
    }
    saveLoading.value = true
    try {
      let res = await updateReasonApi(formData.id, value)
      if (res) {
        ElMessageBox.confirm(t('是否确认验证通过'), t('确认操作'), {
          confirmButtonText: t('确认'),
          cancelButtonText: t('取消'),
          type: 'warning'
        }).then(
          async () => {
            saveLoading.value = true
            try {
              const res = await submitApi(currentRow.value!.id!, { approved: true })
              if (res) {
                ElMessage.success(t('验证通过成功'))
                refresh()
                dialogVisible.value = false
              }
            } catch {}
          },
          (r) => {
            console.log(r)
          }
        )
      }
    } catch {
      ElMessage.error(t('保存失败'))
    } finally {
      saveLoading.value = false
    }
  }
}
const reject = () => {
  ElMessageBox.prompt(t('是否确认验证驳回'), t('确认操作'), {
    confirmButtonText: t('确认'),
    cancelButtonText: t('取消'),
    type: 'warning',
    inputValidator: (value: string) => {
      return !!(value ?? '').trim().length
    },
    inputErrorMessage: t('请填写验证驳回理由')
  }).then(
    async ({ value }) => {
      saveLoading.value = true
      try {
        const res = await submitApi(currentRow.value!.id!, { approved: false, remark: value })
        if (res) {
          ElMessage.success(t('验证驳回成功'))
          refresh()
          dialogVisible.value = false
        }
      } catch {
      } finally {
        saveLoading.value = false
      }
    },
    (r) => {
      console.log(r)
    }
  )
}
</script>

<template>
  <Descriptions :class="prefixCls" :title="t('原因分析')" :data="dataList" :schema="schemas" />
  <Drawer
    v-model="dialogVisible"
    :title="t('原因分析及行动计划')"
    :fullscreen="false"
    default-fullscreen
  >
    <ReasonForm
      ref="formRef"
      :action-type="actionType"
      :schemas="formSchemas"
      :current-row="currentRow"
    />

    <template #footer>
      <BaseButton v-if="readonly && !isValidate" type="default" @click="dialogVisible = false">
        {{ t('关闭') }}
      </BaseButton>
      <BaseButton
        v-if="!readonly || isValidate"
        type="primary"
        :loading="saveLoading"
        @click="save()"
      >
        {{ t('保存') }}
      </BaseButton>
      <BaseButton
        v-if="!readonly && !isValidate"
        type="primary"
        :loading="saveLoading"
        @click="submit()"
      >
        {{ t('提交') }}
      </BaseButton>
      <BaseButton v-if="isValidate" type="primary" :loading="saveLoading" @click="approve()">
        {{ t('审批') }}
      </BaseButton>
      <BaseButton v-if="isValidate" type="danger" :loading="saveLoading" @click="reject()">
        {{ t('驳回') }}
      </BaseButton>
    </template>
  </Drawer>
  <Dialog v-model="visible" :fullscreen="false" :title="t('转办')">
    <ReasonTransferForm
      ref="transferFormRef"
      :current-row="transferRow"
      :users="usersRef"
      :departments="departmentsRef"
    />
    <template #footer>
      <BaseButton type="primary" :loading="saveLoading" @click="transfer">
        {{ t('保存') }}
      </BaseButton>
    </template>
  </Dialog>
</template>
<style lang="less" scoped>
@prefix-cls: ~'@{elNamespace}-descriptions';

.@{prefix-cls} {
  :deep(&__label) {
    display: none;
  }

  :deep(&__content) {
    padding: 0 !important;
  }
}
</style>
