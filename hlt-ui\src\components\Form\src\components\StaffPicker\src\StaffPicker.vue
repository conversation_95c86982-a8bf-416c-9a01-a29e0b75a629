import { ElCard } from 'element-plus';
<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { Department, User } from '@/api/user/types'
import { Icon } from '@/components/Icon'
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { ElCol, ElInput, ElRow, ElTag, ElTreeV2 } from 'element-plus'

const { getPrefixCls } = useDesign()

const { t } = useI18n()
const prefixCls = getPrefixCls('breadcrumb')

const props = defineProps({
  user: {
    type: Number as PropType<ID>
  },
  readonly: {
    type: Boolean as PropType<boolean>,
    default: () => false
  },
  users: {
    type: Array as PropType<User[]>,
    default: () => []
  },
  departments: {
    type: Array as PropType<Department[]>,
    default: () => []
  }
})
const maps = computed<{
  userMap: Map<number, User>
  deptMap: Map<number, Department & { users: User[] }>
}>(() => {
  const userMap = new Map<number, User>()
  const deptMap = new Map<number, Department & { users: User[] }>()
  for (const department of props.departments) {
    deptMap.set(department.id, { ...department, users: [] })
  }
  for (const user of props.users) {
    userMap.set(user.id, user)
    if (deptMap.has(user.deptId)) {
      deptMap.get(user.deptId)!.users.push(user)
    }
  }
  return { userMap, deptMap }
})
const visible = ref(false)
const searchValue = ref<string>()
const currentDept = ref<number>(-1)
const expandKeys = computed<number[]>(() => {
  const { deptMap } = unref(maps)
  return Array.from(deptMap.values()).map((department) => department.id)
})
const userList = ref<User[]>(props.users)
const departmentTree = computed<(Department & { label: string })[]>(() => {
  const roots: (Department & { label: string })[] = []
  const departments: Record<number, Department & { users: User[]; label: string }> = {}
  const { deptMap } = unref(maps)
  deptMap.forEach((department) => {
    departments[department.id] = {
      label: `${department.name}(${department.users.length})`,
      ...department
    }
  })
  for (const department of Object.values(departments)) {
    if (!department.parentId || !departments[department.parentId]) {
      roots.push(department)
    } else {
      const parent = departments[department.parentId]
      parent.children = parent.children ?? []
      parent.children.push(department)
    }
  }
  const sort = (departments: Department[]) => {
    for (const department of departments) {
      if (!!department.children?.length) {
        sort(department.children)
      }
    }
    departments.sort((d1, d2) => d1.sort - d2.sort)
  }
  sort(roots)
  return [
    { id: -1, name: '全部人员', label: `全部人员(${props.users.length})` } as Department & {
      label: string
    },
    ...roots
  ]
})
const isChineseInput = ref(false)

// 绑定输入事件
document.addEventListener('compositionstart', () => (isChineseInput.value = true))

document.addEventListener('compositionend', () => (isChineseInput.value = false))

watch(
  () => [currentDept.value, searchValue.value],
  ([dept, search]) => {
    if (!dept || dept === -1) {
      userList.value = !!(search as string)?.length
        ? props.users.filter((user) =>
            user.name.toLowerCase().includes(((search as string) ?? '').toLowerCase())
          )
        : props.users
    } else {
      const { deptMap } = unref(maps)
      userList.value = !!(search as string)?.length
        ? (deptMap.get(dept as number)?.users ?? []).filter((item) =>
            item.name.toLowerCase().includes(((search as string) ?? '').toLowerCase())
          )
        : (deptMap.get(dept as number)?.users ?? [])
    }
  }
)
const currentUser = ref<User | undefined>(maps.value.userMap.get(props.user as number))
watch(
  () => props.user,
  (val) => {
    currentUser.value = maps.value.userMap.get(val as number)
  }
)
const emit = defineEmits(['change'])
const handleChange = (u: User) => {
  const currentU = unref(currentUser)
  if (u.id !== currentU?.id) {
    currentUser.value = u
    emit('change', {
      user: u,
      leader: props.users.find((item) => item.id === u?.leader),
      indirectLeader: props.users.find((item) => item.id === u?.indirectLeader)
    })
  }
}
const handleClick = (node: Department) => {
  currentDept.value = node.id
}

const Search = <Icon icon="ant-design:search-outlined" />
</script>

<template>
  <BaseButton type="default" v-if="!!currentUser && readonly">
    <ElSpace>
      <ElTag>{{ maps.deptMap.get(currentUser?.deptId)?.name ?? '-' }}</ElTag
      >{{ currentUser.name }}
    </ElSpace>
  </BaseButton>
  <ElTooltip
    :trigger="'click'"
    :visible="visible"
    effect="light"
    v-else
    popper-class="!p-0 w-600px"
  >
    <template #content>
      <ElCard
        shadow="never"
        :class="prefixCls"
        @mouseleave="
          () => {
            if (!isChineseInput) {
              visible = false
            }
          }
        "
      >
        <template #header>
          <div class="flex justify-between items-center">
            <ElSpace
              ><span class="font-700 text-14px"> {{ t('人员选择') }} </span>
              <ElInput
                v-model="searchValue"
                clearable
                :placeholder="t('请输入姓名查找')"
                :suffix-icon="Search"
              />
            </ElSpace>
            <BaseButton text @click="visible = false">
              <Icon icon="ant-design:close-outlined" />
            </BaseButton>
          </div>
        </template>
        <ElRow>
          <ElCol
            :span="12"
            class="h-250px overflow-y-auto border-r-1px border-r-solid border-r-[var(--el-border-color)]"
          >
            <ElTreeV2
              :item-size="32"
              :height="250"
              :default-expanded-keys="expandKeys"
              :data="departmentTree"
              :current-node-key="currentDept"
              node-key="id"
              :highlight-current="true"
              :expand-on-click-node="false"
              @node-click="handleClick"
              default-expand-all
            />
          </ElCol>
          <ElCol :span="12" class="pl-12px h-250px">
            <ElTreeV2
              :item-size="32"
              :height="250"
              :data="userList"
              :props="{ label: 'name' }"
              :current-node-key="currentUser?.id"
              node-key="id"
              :highlight-current="true"
              @node-click="handleChange"
            />
            <!--    <ElScrollbar class="h-full">
              <ElRadioGroup v-model="userId">
                <ElRadio
                  class="w-full"
                  v-for="(u, idx) in userList.slice(10)"
                  :key="idx"
                  :label="u.id"
                  @change="handleChange(u)"
                  >{{ u.name }}</ElRadio
                >
              </ElRadioGroup>
            </ElScrollbar> -->
          </ElCol>
        </ElRow>
      </ElCard>
    </template>
    <BaseButton type="default" v-if="!!currentUser" @click="visible = true">
      <ElSpace>
        <ElTag>{{
          props.departments.find((item) => item.id === currentUser?.deptId)?.name ?? '-'
        }}</ElTag
        >{{ currentUser.name }}
      </ElSpace>
    </BaseButton>
    <BaseButton type="primary" v-else @click="visible = true">{{ t('请选择') }}</BaseButton>
  </ElTooltip>
</template>
<style lang="less" scoped>
@prefix-cls: ~'@{elNamespace}-card';

.@{prefix-cls} {
  :deep(&__header) {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  :deep(&__body) {
    padding: 0;
  }
}
</style>
