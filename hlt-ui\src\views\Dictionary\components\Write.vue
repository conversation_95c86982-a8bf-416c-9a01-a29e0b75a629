<script setup lang="ts">
import { Dictionary, DictionaryCategory } from '@/api/dictionary/types'
import { ComponentNameEnum, Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { useI18n } from '@/hooks/web/useI18n'
import { useValidator } from '@/hooks/web/useValidator'

const { required, whitespace } = useValidator()
const { t } = useI18n()
const props = defineProps({
  currentRow: {
    type: Object as PropType<Nullable<Dictionary>>,
    default: () => null
  },
  formSchema: {
    type: Array as PropType<FormSchema[]>,
    default: () => []
  }
})

const rules = reactive({
  priority: [required()],
  description: [whitespace()]
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose, getFieldValue } = formMethods

const submit = async () => {
  const elForm = await getElFormExpose()
  const valid = await elForm?.validate().catch((err) => {
    console.log(err)
  })
  if (valid) {
    const formData = await getFormData<Dictionary>()
    return formData
  }
}

const schemas = computed(() => {
  const optionsSchema = props.formSchema.find((item) => item.field === 'options')
  if (optionsSchema) {
    optionsSchema.component = props.currentRow?.tree
      ? ComponentNameEnum.TREE
      : ComponentNameEnum.FORM_LIST
    if (props.currentRow?.category === DictionaryCategory.FACTORY) {
      optionsSchema.componentProps.children[0].component = ComponentNameEnum.TEXT
      optionsSchema.componentProps.readonly = true
    } else {
      optionsSchema.componentProps.children[0].component = ComponentNameEnum.INPUT
      optionsSchema.componentProps.readonly = false
    }
    if (!!optionsSchema.componentProps?.children?.length) {
      optionsSchema.componentProps.children[0].formItemProps = optionsSchema.componentProps
        .children[0].formItemProps ?? {
        rules: []
      }
      if (!!optionsSchema.componentProps.children[0].formItemProps.rules) {
        ;(optionsSchema.componentProps.children[0].formItemProps.rules as any[]).push({
          asyncValidator: async (rule, value) => {
            const options = await getFieldValue('options')
            const idx = parseInt(rule.field.split('.')[1], 10)
            if (
              (options ?? [])
                .filter((_option, i) => i !== idx)
                .some((option) => option.name === value)
            ) {
              throw `${t('字典选项已存在')}[${value}]`
            }
          }
        })
      }
    }
  }
  return props.formSchema
})
watch(
  () => props.currentRow,
  (currentRow) => {
    if (!currentRow) return
    setValues(currentRow)
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  submit
})
</script>

<template>
  <Form labelPosition="top" :rules="rules" :schema="schemas" @register="formRegister" />
</template>
