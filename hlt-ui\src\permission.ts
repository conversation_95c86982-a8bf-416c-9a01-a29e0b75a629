import { useTodoStoreWithOut } from './store/modules/todo'
import { NO_REDIRECT_WHITE_LIST } from '@/constants'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { useTitle } from '@/hooks/web/useTitle'
import { useAppStoreWithOut } from '@/store/modules/app'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { useUserStoreWithOut } from '@/store/modules/user'
import type { RouteRecordRaw } from 'vue-router'
import { getProfile } from './api/login'
import router from './router'
import { getTodoApi } from './api/dashboard'

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()

router.beforeEach(async (to, from, next) => {
  start()
  loadStart()
  await new Promise((resolve) => {
    const interval = setInterval(() => {
      if ((window as any).__inited__) {
        clearInterval(interval)
        resolve(true)
      }
    }, 200)
  })
  const permissionStore = usePermissionStoreWithOut()
  const appStore = useAppStoreWithOut()
  const todoStore = useTodoStoreWithOut()
  const userStore = useUserStoreWithOut()
  if (userStore.getUserInfo) {
    if (to.path === '/login') {
      next({ path: '/' })
    } else {
      const { data } = await getTodoApi()
      todoStore.setTodo(data)
      await permissionStore.generateRoutes(
        'static',
        userStore.getSysRouters,
        userStore.getPermissions
      )
      if (permissionStore.getIsAddRouters) {
        next()
        return
      }

      // 开发者可根据实际情况进行修改
      const roleRouters = userStore.getRoleRouters || []

      // 是否使用动态路由
      if (appStore.getDynamicRouter) {
        appStore.serverDynamicRouter
          ? await permissionStore.generateRoutes('server', roleRouters as AppCustomRouteRecordRaw[])
          : await permissionStore.generateRoutes('frontEnd', roleRouters as string[])
      } else {
        // await permissionStore.generateRoutes('static')
      }
      permissionStore.getAddRouters.forEach((route) => {
        router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
      })
      const redirectPath = from.query.redirect || to.path
      const redirect = decodeURIComponent(redirectPath as string)
      const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
      permissionStore.setIsAddRouters(true)
      next(nextData)
    }
  } else {
    await permissionStore.generateRoutes('static')
    if (NO_REDIRECT_WHITE_LIST.indexOf(to.matched?.[0]?.path) !== -1) {
      next()
    } else if (to.path === '/:path(.*)*') {
      next('/')
    } else {
      await getProfile()
      // await next(`/login`) // 否则全部重定向到登录页
    }
  }
})

router.afterEach((to) => {
  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
})
