<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { NodeState, Reason, ReasonDetail, ReasonDetailType } from '@/api/problem/types'
import { getReasonListApi } from '@/api/problem'
import { Descriptions, DescriptionsSchema } from '@/components/Descriptions'
import ReasonDetailTable from './ReasonDetailTables.vue'
import { Table, TableColumn, TableSlotDefault } from '@/components/Table'
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { propTypes } from '@/utils/propTypes'

const { t } = useI18n()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('log')
const props = defineProps({
  readonly: propTypes.bool.def(false),
  id: String as PropType<ID>
})

const currentRow = ref<Reason>()
const genFormData = (row: Reason) => {
  const {
    id,
    configs,
    details,
    category,
    subCategory,
    improvement,
    unqualityType,
    unqualityCode,
    estimatedFinishOn,
    validateResult,
    state,
    stateIdx
  } = row
  const config = configs.find((item) => item.state === NodeState.ANALYZE)
  const detailRecord: Record<ReasonDetailType, ReasonDetail[]> = {
    [ReasonDetailType.EXPOSE]: [],
    [ReasonDetailType.PRODUCE]: [],
    [ReasonDetailType.SYSTEM]: []
  }
  for (const detail of details) {
    detailRecord[detail.type].push(detail)
  }
  currentRow.value = {
    id,
    category,
    subCategory,
    improvement: improvement ?? undefined,
    unqualityType: unqualityType ?? undefined,
    unqualityCode: unqualityCode ?? undefined,
    estimatedFinishOn,
    details: detailRecord,
    ownerName: config?.ownerName,
    ownerDepartment: config?.ownerDepartment,
    validateResult: validateResult ?? undefined,
    state,
    stateIdx
  } as any
}
const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const res = await getReasonListApi(props.id!)
    const { data } = res
    if (!!currentRow.value) {
      const row = data.find((item) => item.id === currentRow.value?.id)
      if (!!row) {
        genFormData(row)
      }
    }
    return {
      list: data,
      total: data.length
    }
  }
})
const { loading, dataList } = tableState
const { refresh } = tableMethods

const expandSchemas: DescriptionsSchema[] = [
  {
    field: 'category',
    label: t('原因类别(一级)')
  },
  {
    field: 'subCategory',
    label: t('原因类别(二级)')
  },
  {
    field: 'ownerName',
    label: t('原因负责人')
  },
  {
    field: 'ownerDepartment',
    label: t('部门')
  },
  {
    field: 'details',
    label: t('原因明细'),
    span: 24,
    slots: {
      default: ({ details = [] }) => {
        return !!details.length ? <ReasonDetailTable v-model={details} /> : <>-</>
      }
    }
  },
  {
    field: 'improvement',
    label: t('行动计划'),
    span: 24,
    slots: {
      default: ({ improvement }: any) => {
        return !!improvement?.length ? (
          <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
            {improvement}
          </p>
        ) : (
          <>-</>
        )
      }
    }
  },
  {
    field: 'unqualityType',
    label: t('不良类别')
  },
  {
    field: 'unqualityCode',
    label: t('不良代码')
  },
  {
    field: 'estimatedFinishOn',
    label: t('预计完成日期')
  },
  {
    field: 'validateResult',
    label: t('效果验证'),
    span: 24,
    slots: {
      default: ({ validateResult }: any) => {
        return !!validateResult?.length ? (
          <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
            {validateResult}
          </p>
        ) : (
          <>-</>
        )
      }
    }
  }
]

const tableColumns: TableColumn[] = [
  {
    field: 'expand',
    type: 'expand',
    slots: {
      default: (data: TableSlotDefault) => {
        const { row } = data
        return <Descriptions column={2} direction={'vertical'} schema={expandSchemas} data={row} />
      }
    }
  },
  {
    field: 'index',
    label: t('序号'),
    type: 'index'
  },
  {
    field: 'category',
    label: t('原因类别(一级)'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'subCategory',
    label: t('原因类别(二级)'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'unqualityType',
    label: t('不良类别'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'unqualityCode',
    label: t('不良代码'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'improvement',
    label: t('行动计划'),
    width: 120
  },
  {
    field: 'status',
    label: t('状态'),
    width: 120
  },
  {
    field: 'estimatedFinishOn',
    label: t('预计完成日期'),
    width: 120
  },
  {
    field: 'finishOn',
    label: t('实际完成日期'),
    width: 120
  },
  {
    field: 'responsible',
    label: t('责任人'),
    formatter: (entity: Recordable, __: TableColumn) => {
      return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerName
    }
  },
  {
    field: 'responsibleDep',
    label: t('部门'),
    formatter: (entity: Recordable, __: TableColumn) => {
      return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerDepartment
    }
  },
  {
    field: 'validateResult',
    label: t('验证结果')
  },
  {
    field: 'validateOn',
    label: t('验证日期')
  }
]
const schemas: DescriptionsSchema[] = reactive([
  {
    field: 'reasons',
    label: t('原因分析'),
    span: 24,
    slots: {
      default: (data) => {
        return (
          <Table
            align="center"
            rowKey="id"
            headerAlign="center"
            expandRowKeys={(data ?? []).map((item) => item.id)}
            columns={tableColumns}
            data={data}
            loading={loading.value}
            onRegister={tableRegister}
            onRefresh={refresh}
          />
        )
      }
    }
  }
])
</script>

<template>
  <Descriptions direction="vertical" :class="prefixCls" :data="dataList" :schema="schemas" />
</template>
<style lang="less" scoped>
@prefix-cls: ~'@{elNamespace}-descriptions';

.v-log {
  :deep(> .v-descriptions-content) {
    padding-top: 0 !important;
    margin-top: -21px;
  }
}
</style>
