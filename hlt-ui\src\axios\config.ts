import { useI18n } from '@/hooks/web/useI18n'
import { AxiosResponse, AxiosRequestHeaders, InternalAxiosRequestConfig } from './types'
import { ElMessage } from 'element-plus'
import qs from 'qs'
import { SUCCESS_CODE } from '@/constants'
import { useUserStoreWithOut } from '@/store/modules/user'

const defaultRequestInterceptors = (config: InternalAxiosRequestConfig) => {
  if (
    config.method === 'post' &&
    (config.headers as AxiosRequestHeaders)['Content-Type'] === 'application/x-www-form-urlencoded'
  ) {
    config.data = qs.stringify(config.data)
  }
  if (config.method === 'get' && config.params) {
    let url = config.url as string
    url += '?'
    const keys = Object.keys(config.params)
    for (const key of keys) {
      if (config.params[key] !== void 0 && config.params[key] !== null) {
        url += `${key}=${encodeURIComponent(config.params[key])}&`
      }
    }
    url = url.substring(0, url.length - 1)
    config.params = {}
    config.url = url
  }
  return config
}

const defaultResponseInterceptors = (response: AxiosResponse) => {
  if (response?.config?.responseType === 'blob') {
    // 如果是文件流，直接过
    return response
  } else if (response.data.code === SUCCESS_CODE || response.data.success) {
    return response.data
  } else {
    const { t } = useI18n()
    ElMessage.error(t(response?.data?.message))
    if (response?.data?.statusCode === 401) {
      const userStore = useUserStoreWithOut()
      const redirect = userStore.isRedirect
      if (!!userStore.getUserInfo) {
        userStore.logout(false)
      } else {
        userStore.setRedirect(false)
      }
      if (!!response?.data?.redirectUrl?.length) {
        let { redirectUrl } = response.data
        if (redirect) {
          redirectUrl = redirectUrl.replace('login', '')
        } else {
          redirectUrl += `&redirect=${encodeURIComponent(window.location.pathname)}`
        }
        window.location.replace(redirectUrl)
      }
    } else {
      return response?.data
    }
  }
}

export { defaultResponseInterceptors, defaultRequestInterceptors }
