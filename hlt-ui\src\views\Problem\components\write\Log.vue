<script setup lang="ts">
import { ID } from '@/api/base/types'
import { getLogApi } from '@/api/problem'
import { ProblemOperateLog } from '@/api/problem/types'
import { useI18n } from '@/hooks/web/useI18n'
import dayjs from 'dayjs'
import { ElTimeline, ElTimelineItem } from 'element-plus'
const { t } = useI18n()
const props = defineProps({
  id: String as PropType<Nullable<ID>>
})
const logs = ref<ProblemOperateLog[]>([])
watchEffect(async () => {
  if (!!props.id) {
    const { data = [] } = await getLogApi(props.id)
    logs.value = data
  }
})
</script>
<template>
  <ElTimeline>
    <ElTimelineItem
      type="primary"
      hollow
      v-for="(log, idx) in logs"
      :key="idx"
      :timestamp="dayjs(log.createdAt).format('YYYY-MM-DD HH:mm:ss')"
      >{{ `${log.operatorName}${t(log.action.message)}` }}</ElTimelineItem
    >
  </ElTimeline>
</template>
