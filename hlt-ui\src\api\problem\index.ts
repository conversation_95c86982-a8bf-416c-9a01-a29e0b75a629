import request from '@/axios'
import { AxiosHeaders, AxiosResponse } from 'axios'
import { UploadFile, UploadUserFile } from 'element-plus'
import saveAs from 'file-saver'
import type { ID, Page, Result } from '../base/types'
import { PATH_URL } from './../../axios/service'
import { PageRequest } from './../../utils/index'
import { ProblemOperateLog, Reason, type Problem } from './types'

const BASE_API = '/problem'

export const getListApi = async (data: {
  query?: Record<string, any>
  page?: { pageNo: number; pageSize: number }
  sort?: { prop: string; order: 'ascending' | 'descending' | null }
}) => {
  return request.post<Page<Problem>>({
    url: `${BASE_API}/list`,
    data: new PageRequest(data)
  })
}

export const getApi = async (id: ID) => {
  return request.get<Problem>({ url: `${BASE_API}/${id}` })
}

export const getByCodeApi = async (code: string) => {
  return request.get<Problem>({ url: `${BASE_API}/code/${code}` })
}

export const saveOrUpdateApi = async (entity: Problem) => {
  return request.post<Problem>({ url: `${BASE_API}/`, data: entity })
}

export const downloadTplApi = async () => {
  const { data, headers } = (await request.get({
    url: `${BASE_API}/download/tpl`,
    responseType: 'blob'
  })) as unknown as AxiosResponse
  let fileName
  if (!!headers) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(headers['content-disposition'])
    fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
  }
  saveAs(data, fileName)
}

export const uploadTplApi = async (file: UploadUserFile) => {
  const formData = new FormData()
  formData.append('file', file.raw!, file.name)
  const { data, headers } = (await request.post({
    url: `${BASE_API}/data/import`,
    data: formData,
    headers: AxiosHeaders.from().setContentType('multipart/form-data'),
    responseType: 'blob'
  })) as unknown as AxiosResponse
  if (data.type === 'application/json') {
    return new Promise((resolve) => {
      const fr = new FileReader()
      fr.onload = function () {
        const { code, message } = JSON.parse(fr.result as string)
        if (code === 1) {
          ElMessage.error(message)
        }
        resolve({ code })
      }
      fr.readAsText(data)
    })
  } else {
    ElMessage.error('导入失败,请下载文件查看错误详情')
    let fileName
    if (!!headers) {
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      const matches = filenameRegex.exec(headers['content-disposition'])
      fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
    }
    return {
      code: 1,
      file: {
        url: URL.createObjectURL(data),
        name: fileName
      }
    }
  }
}

export const exportListApi = async (
  req: {
    query?: Record<string, any>
    sort?: { prop: string; order: 'ascending' | 'descending' | null }
  },
  ids: ID[]
) => {
  const { data, headers } = (await request.post({
    url: `${BASE_API}/export`,
    data: { ...new PageRequest(req), ids },
    responseType: 'blob'
  })) as unknown as AxiosResponse
  let fileName
  if (!!headers) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(headers['content-disposition'])
    fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
  }
  saveAs(data, fileName)
}

export const exportPdfApi = async (id: ID) => {
  const { data, headers } = (await request.get({
    url: `${BASE_API}/pdf/${id}`,
    responseType: 'blob'
  })) as unknown as AxiosResponse
  let fileName
  if (!!headers) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(headers['content-disposition'])
    fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
  }
  saveAs(data, fileName)
}

export const submitApi = async (id: ID) => {
  return request.post<Problem>({ url: `${BASE_API}/submit/${id}` })
}

export const uploadApi = async (id: ID, file: UploadFile, type: 'good' | 'bad') => {
  const form = new FormData()
  form.append('file', file.raw!, file.name)
  return request.post<Problem>({
    url: `${BASE_API}/upload/${id}/${type}`,
    data: form,
    headers: AxiosHeaders.from().setContentType('multipart/form-data')
  })
}

export const getPreviewURLApi = (id: ID, key: string, type: 'good' | 'bad') => {
  const prefix = !PATH_URL?.length ? BASE_API : `${PATH_URL}${BASE_API}`
  return `${prefix}/attachment/${type}/${id}/${key}`
}

export const previewApi = async (id: ID, key: string, type: 'good' | 'bad') => {
  return request.get({ url: `${BASE_API}/attachment/${type}/${id}/${key}` })
}

export const deleteImageApi = async (id: ID, key: string, type: 'good' | 'bad') => {
  return request.delete({ url: `${BASE_API}/attachment/${type}/${id}/${key}` })
}

export const deleteApi = async (ids: ID[]) => {
  return request.delete<Result>({ url: `${BASE_API}/`, data: { ids } })
}

export const getLogApi = async (id: ID) => {
  return request.get<ProblemOperateLog[]>({ url: `${BASE_API}/logs/${id}` })
}

export const getReasonListApi = async (id: ID) => {
  return request.get<Reason[]>({ url: `${BASE_API}/${id}/reason` })
}

export const saveReasonApi = async (id: ID, reason: Partial<Reason>) => {
  return request.post<Reason[]>({ url: `${BASE_API}/${id}/reason`, data: reason })
}

export const delReasonApi = async (id: ID, ids: ID[]) => {
  return request.delete<Reason[]>({ url: `${BASE_API}/${id}/reason`, data: { ids } })
}

export const updateReasonApi = async (id: ID, reason: Partial<Reason>) => {
  return request.put<Reason[]>({ url: `${BASE_API}/${id}/reason`, data: reason })
}

export const sendEmailApi = async () => {
  return request.post({ url: `${BASE_API}/sendMails` })
}
