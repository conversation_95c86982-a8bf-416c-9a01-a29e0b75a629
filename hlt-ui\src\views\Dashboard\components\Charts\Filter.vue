<script setup lang="ts">
import {
  ElForm,
  ElFormItem,
  ElOption,
  ElRadioButton,
  ElRadioGroup,
  ElRow,
  ElCol,
  ElSelect
} from 'element-plus'
import { ref } from 'vue'
import { useDashboardStore } from '@/store/modules/dashboard'
import { getListApi } from '@/api/dictionary'
import { SearchParams } from '@/api/dashboard/types'
import { DictionaryCategory } from '@/api/dictionary/types'
import { useI18n } from '@/hooks/web/useI18n'
const { t } = useI18n()
const store = useDashboardStore()

const init = async () => {
  try {
    const res = await getListApi({
      category: [
        DictionaryCategory.CUSTOMER,
        DictionaryCategory.PRODUCT_STEP,
        DictionaryCategory.BUSINESS_UNIT,
        DictionaryCategory.FACTORY,
        DictionaryCategory.UNQUALITY_TYPE,
        DictionaryCategory.REASON_TYPE
      ]
    })
    const options: Record<string, string[]> = {}
    for (const datum of res.data) {
      options[datum.category] = datum.options.map((op) => {
        if (!!op.children?.length) {
          store.setOptions(
            op.name,
            op.children.map((c) => c.name)
          )
        }
        return op.name
      })
    }
    store.setOptions(options)
  } catch (e) {}
}

await init()

const filter = ref<SearchParams>(store.filter ?? {})
const submitForm = () => {
  store.setFilter(unref(filter))
}
</script>
<template>
  <ElRow :gutter="20" justify="space-between">
    <ElCol>
      <ElForm label-width="0" :model="store.filter" inline>
        <ElFormItem style="margin-bottom: 0">
          <ElRadioGroup v-model="filter.factory" @change="submitForm">
            <ElRadioButton>{{ t('全球') }}</ElRadioButton>
            <ElRadioButton v-for="item in store.options['FACTORY']" :key="item" :label="item">
              {{ t(item) }}
            </ElRadioButton>
          </ElRadioGroup>
        </ElFormItem>
        <ElFormItem style="margin-bottom: 0">
          <ElSelect
            clearable
            @change="submitForm"
            v-model="filter.businessUnit"
            filterable
            class="m-2"
            multiple
            collapse-tags
            :placeholder="t('全部')"
            onchange=""
          >
            <ElOption
              v-for="item in store.options['BUSINESS_UNIT']"
              :key="item"
              :label="t(item)"
              :value="item"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem style="margin-bottom: 0">
          <ElSelect
            clearable
            @change="submitForm"
            v-model="filter.customer"
            class="m-2"
            multiple
            collapse-tags
            :placeholder="t('全部')"
          >
            <ElOption
              v-for="item in store.options['CUSTOMER']"
              :key="item"
              :label="t(item)"
              :value="item"
            />
          </ElSelect>
        </ElFormItem>
        <ElFormItem style="margin-bottom: 0">
          <ElSelect
            clearable
            @change="submitForm"
            v-model="filter.productStep"
            class="m-2"
            multiple
            collapse-tags
            :placeholder="t('全部')"
          >
            <ElOption
              v-for="item in store.options['PRODUCT_STEP']"
              :key="item"
              :label="t(item)"
              :value="item"
            />
          </ElSelect>
        </ElFormItem>
      </ElForm>
    </ElCol>
  </ElRow>
</template>
