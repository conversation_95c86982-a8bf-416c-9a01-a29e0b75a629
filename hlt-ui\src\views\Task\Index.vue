<script setup lang="tsx">
import { getOptionsByCategoriesApi } from '@/api/dictionary'
import { DictionaryCategory } from '@/api/dictionary/types'
import { getPreviewURLApi } from '@/api/problem'
import {
  NodeState,
  Problem,
  ReasonStateOptions,
  ReasonStateSelectOptions
} from '@/api/problem/types'
import { getApi, getListApi } from '@/api/task'
import { BaseButton } from '@/components/Button'
import { ComponentNameEnum, FormSchema } from '@/components/Form'
import { hasPermi } from '@/components/Permission'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { convertRem } from '@/rem'
import { useLocaleStore } from '@/store/modules/locale'
import { useUserStore } from '@/store/modules/user'
import { Operators } from '@/utils'
import { useResizeObserver } from '@vueuse/core'
import { ElCol, ElImage, ElRow, ElSpace, ElTag } from 'element-plus'
import Write from './components/Write/Index.vue'

defineOptions({
  name: 'TaskIndex'
})

const localeStore = useLocaleStore()
const { t } = useI18n()
const userStore = useUserStore()
const drawerVisible = ref(false)
const currentRow = ref<Partial<Problem> | null>(null)
const actionType = ref('')
const action = (row: Partial<Problem> | null, type: string) => {
  actionType.value = type
  currentRow.value = row
  drawerVisible.value = true
}
const searchParams = ref<Recordable>({})
const sortParams = ref<{ prop: string; order: 'ascending' | 'descending' | null } | undefined>({
  prop: 'createdOn',
  order: 'descending'
})
const setSearchParams = (params: any) => {
  searchParams.value = params
  refresh()
}

const allSchemas = ref<Recordable>()
const formSchemas = ref<FormSchema[]>()

onBeforeMount(async () => {
  if (hasPermi('task:process')) {
    const fullHash = window.location.hash
    let search: string
    if (!!fullHash?.length) {
      const segements = fullHash.split('?')
      search = segements.slice(1).join('')
    } else {
      search = window.location.search
    }
    const hashParams = new URLSearchParams(search)
    const entityId = hashParams.get('entityId')
    if (!!entityId) {
      const { data } = await getApi(entityId)
      action(data, 'detail')
    }
  }
  const { data } = await getOptionsByCategoriesApi([
    DictionaryCategory.BUSINESS_UNIT,
    DictionaryCategory.CUSTOMER,
    DictionaryCategory.FACTORY,
    DictionaryCategory.PRODUCT_LINE,
    DictionaryCategory.PRODUCT_STEP,
    DictionaryCategory.MACHINE_TYPE,
    DictionaryCategory.MACHINE_CATEGORY
  ])
  const crudSchemas: CrudSchema[] = [
    {
      field: 'selection',
      type: 'selection',
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      }
    },
    {
      field: 'index',
      label: t('序号'),
      type: 'index',
      table: {
        fixed: 'left',
        slots: {
          default: ({ $index }: { $index: number }) => {
            const currentPage = tableState.currentPage.value || 1
            const pageSize = tableState.pageSize.value || 10
            const seq = (currentPage - 1) * pageSize + $index + 1
            return <span>{seq}</span>
          }
        }
      },
      search: {
        hidden: true
      }
    },
    {
      field: 'code',
      label: t('问题编号'),
      table: {
        fixed: 'left',
        width: 180,
        sortable: 'custom',
        showOverflowTooltip: true,
        formatter: (row, _, cellValue) => {
          if (hasPermi('task:view') || hasPermi('task:process')) {
            return (
              <BaseButton
                type="info"
                link
                onClick={() => action(row, 'detail')}
                style={{ userSelect: 'text' }}
              >
                {cellValue}
              </BaseButton>
            )
          }
          return cellValue
        }
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'createdOn',
      label: t('创建日期'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: ComponentNameEnum.CT_DATE,
        componentProps: {
          type: 'date',
          operate: Operators.BETWEEN,
          valueFormat: 'YYYY-MM-DD'
        }
      }
    },
    {
      field: 'machineType',
      label: t('机型'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.MACHINE_TYPE] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      detail: {
        span: 24
      },
      formatter: (_, __, val) => t(val)
    },
    {
      field: 'customer',
      label: t('客户'),
      table: {
        width: 120,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.CUSTOMER] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, val) => t(val)
    },
    {
      field: 'projectCode',
      label: t('项目编号'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'workOrderCode',
      label: t('工单号'),
      table: {
        width: 120,
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'workOrderNum',
      label: t('工单数量'),
      table: {
        width: 160,
        sortable: 'custom'
      },
      search: { hidden: true }
    },
    {
      field: 'factory',
      label: t('和而泰工厂'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.FACTORY] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, val) => t(val)
    },
    {
      field: 'businessUnit',
      label: t('事业部'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.BUSINESS_UNIT] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, val) => t(val)
    },
    {
      field: 'creatorName',
      label: t('创建人'),
      table: {
        width: 120,
        sortable: 'custom'
      },
      search: {
        hidden: true
      }
    },
    {
      field: 'productStep',
      label: t('产品阶段'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.PRODUCT_STEP] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, val) => t(val)
    },
    {
      field: 'processStatus',
      label: t('状态'),
      table: {
        width: 120,
        fixed: 'right',
        sortable: 'custom',
        formatter: (entity: Problem) => {
          const reasons = entity.reasons.filter(
            (reason) =>
              (reason.state === NodeState.ANALYZE || reason.state === NodeState.VALIDATE) &&
              !!reason.configs.find(
                (config) =>
                  config.stateIdx === reason.stateIdx && config.ownerId === userStore.userInfo?.id
              )
          )
          return (
            <ElSpace direction="vertical" size={[0, 0]}>
              {reasons.map((reason) => {
                const statusIdxes = reason.configs
                  .filter(
                    (config) =>
                      config.ownerId === userStore.userInfo?.id &&
                      (config.state === NodeState.ANALYZE || config.state === NodeState.VALIDATE)
                  )
                  .map((config) => config.stateIdx ?? 0)
                let status
                if (statusIdxes.length === 1) {
                  status = reason.stateIdx - statusIdxes[0]
                } else if (statusIdxes.includes(reason.stateIdx)) {
                  status = 0
                } else {
                  status = statusIdxes.some((idx) => idx > reason.stateIdx) ? -1 : 1
                }
                return (
                  <ElTag type={status === 0 ? 'warning' : status > 0 ? 'success' : 'info'}>
                    {t(status === 0 ? '待处理' : status > 0 ? '已处理' : '未处理')}
                  </ElTag>
                )
              })}
            </ElSpace>
          )
        }
      },
      search: {
        hidden: true
        /* component: 'Select',
        componentProps: {
          filterable: true,
          options: [
            { label: t('未处理'), value: 'UNPROCESSED' },
            { label: t('待处理'), value: 'PROCESSING' },
            { label: t('已处理'), value: 'PROCESSED' }
          ]
        } */
      }
    },
    {
      field: 'node',
      label: t('当前节点'),
      table: {
        minWidth: 250,
        formatter: (entity: Problem) => {
          const reasons = entity.reasons.filter(
            (reason) =>
              (reason.state === NodeState.ANALYZE || reason.state === NodeState.VALIDATE) &&
              !!reason.configs.find(
                (config) =>
                  config.stateIdx === reason.stateIdx && config.ownerId === userStore.userInfo?.id
              )
          )
          return (
            <ElSpace direction="vertical" size={[0, 0]}>
              {reasons.map((reason) => {
                const current = reason.configs.find((config) => config.state === reason.state)
                const statusIdxes = reason.configs
                  .filter(
                    (config) =>
                      config.ownerId === userStore.userInfo?.id &&
                      (config.state === NodeState.ANALYZE || config.state === NodeState.VALIDATE)
                  )
                  .map((config) => config.stateIdx ?? 0)
                let status
                if (statusIdxes.length === 1) {
                  status = reason.stateIdx - statusIdxes[0]
                } else if (statusIdxes.includes(reason.stateIdx)) {
                  status = 0
                } else {
                  status = statusIdxes.some((idx) => idx > reason.stateIdx) ? -1 : 1
                }
                return (
                  <ElTag type={status === 0 ? 'warning' : status > 0 ? 'success' : 'info'}>
                    {t(ReasonStateOptions[reason.state])}
                    {!!current && !!current.ownerId
                      ? '[' +
                        (current.ownerId === userStore.userInfo?.id ? '我' : current.ownerName) +
                        ']'
                      : ''}
                  </ElTag>
                )
              })}
            </ElSpace>
          )
        }
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: ReasonStateSelectOptions.filter(
            (item) => item.value === NodeState.ANALYZE || item.value === NodeState.VALIDATE
          ).map(({ label, value }) => ({ label: t(label), value }))
        }
      }
    },
    {
      field: 'nextNode',
      label: t('下一节点'),
      table: {
        minWidth: 250,
        formatter: (entity: Problem) => {
          const reasons = entity.reasons.filter(
            (reason) =>
              (reason.state === NodeState.ANALYZE || reason.state === NodeState.VALIDATE) &&
              !!reason.configs.find(
                (config) =>
                  config.stateIdx === reason.stateIdx && config.ownerId === userStore.userInfo?.id
              )
          )
          return (
            <ElSpace direction="vertical" size={[0, 0]}>
              {reasons.map((reason) => {
                const current = reason.configs.find((config) => config.state === reason.state)
                const next = reason.configs.find(
                  (config) => config.stateIdx === (current?.stateIdx ?? -999) + 1
                )
                return (
                  <ElTag>
                    {!!next && !!next.state ? t(ReasonStateOptions[next.state]) : '-'}
                    {!!next && !!next.ownerName?.length ? '[' + next.ownerName + ']' : ''}
                  </ElTag>
                )
              })}
            </ElSpace>
          )
        }
      },
      search: { hidden: true }
    },
    {
      field: 'remark',
      label: t('备注'),
      table: {
        width: 180,
        formatter: (entity: Problem) => {
          const reasons = entity.reasons.filter(
            (reason) =>
              (reason.state === NodeState.ANALYZE || reason.state === NodeState.VALIDATE) &&
              !!reason.configs.find(
                (config) =>
                  config.stateIdx === reason.stateIdx && config.ownerId === userStore.userInfo?.id
              )
          )
          return (
            <ElSpace direction="vertical" size={[0, 0]}>
              {reasons.map((reason) => {
                return <ElTag>{reason.remark ?? '-'}</ElTag>
              })}
            </ElSpace>
          )
        }
      },
      search: {
        hidden: true
      }
    }
  ]
  allSchemas.value = useCrudSchemas(crudSchemas).allSchemas
  formSchemas.value = [
    {
      field: 'createdOn',
      label: t('创建日期'),
      component: ComponentNameEnum.DATE_PICKER,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      }
    },
    {
      field: 'businessUnit',
      label: t('事业部'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.BUSINESS_UNIT] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'factory',
      label: t('和而泰工厂'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.FACTORY] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'productLine',
      label: t('生产线'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.PRODUCT_LINE] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'customer',
      label: t('客户'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.CUSTOMER] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'projectCode',
      label: t('项目编号'),
      component: ComponentNameEnum.INPUT,
      componentProps: {
        showWordLimit: true,
        maxlength: 100
      },
      colProps: { xl: 6, lg: 6, md: 6, span: 12 }
    },
    {
      field: 'machineType',
      label: t('机型'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.MACHINE_TYPE] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'productStep',
      label: t('产品阶段'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.PRODUCT_STEP] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'descriptionDivider',
      label: t('问题描述'),
      component: ComponentNameEnum.DIVIDER,
      colProps: { span: 24 }
    },
    {
      field: 'descriptions.what',
      label: localeStore.isEn ? 'What happened:' : 'What happened:\n发生了什么:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.why',
      label: localeStore.isEn ? 'Why is it an issue:' : 'Why is it an issue:\n为什么是一个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.where',
      label: localeStore.isEn
        ? 'Where was the issue detected:'
        : 'Where was the issue detected:\n在哪里发现这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.when',
      label: localeStore.isEn ? 'When detected:' : 'When detected:\n什么时候发现:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.who',
      label: localeStore.isEn
        ? 'Who detected the issue:'
        : 'Who detected the issue:\n谁发现这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.how_detected',
      label: localeStore.isEn
        ? 'How was the issue detected:'
        : 'How was the issue detected:\n怎么发现的这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.how_many',
      label: localeStore.isEn ? 'How many:' : 'How many:\n发现数量:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'images',
      label: t('图片'),
      colProps: { span: 24 },
      formItemProps: {
        slots: {
          default: ({ formValue }: any) => {
            const { id, goodPartImages = [], badPartImages = [] } = formValue as Problem
            const _goodPartImages = goodPartImages === null ? [] : goodPartImages
            const _badPartImages = badPartImages === null ? [] : badPartImages
            return (
              <ElRow class="w-full">
                <ElCol span={12}>
                  <div class={'w-full border-1px border-solid border-transparent border-r-0'}>
                    <div
                      class={
                        'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-success-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                      }
                    >
                      {t('好件')}
                    </div>
                    <div class={'py-3px px-3px'}>
                      <div
                        class={
                          'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-success-light-5)]'
                        }
                      >
                        <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                          {_goodPartImages.map(({ key }) => {
                            const src = getPreviewURLApi(id, key, 'good')
                            return (
                              <div class={'group relative w-full h-296px flex'}>
                                <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                              </div>
                            )
                          })}
                        </ElSpace>
                      </div>
                    </div>
                  </div>
                </ElCol>
                <ElCol span={12}>
                  <div class={'w-full border-1px border-solid border-transparent'}>
                    <div
                      class={
                        'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-error-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                      }
                    >
                      {t('坏件')}
                    </div>
                    <div class={'py-3px px-3px'}>
                      <div
                        class={
                          'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-error-light-5)]'
                        }
                      >
                        <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                          {_badPartImages.map(({ key }) => {
                            const src = getPreviewURLApi(id, key, 'bad')
                            return (
                              <div class={'group relative w-full h-296px flex'}>
                                <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                              </div>
                            )
                          })}
                        </ElSpace>
                      </div>
                    </div>
                  </div>
                </ElCol>
              </ElRow>
            )
          }
        }
      }
    }
  ]
})

const setSortChange = ({
  prop,
  order
}: {
  prop: string
  order: 'ascending' | 'descending' | null
}) => {
  sortParams.value = order === null ? undefined : { prop, order }
  getList()
}

const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const { currentPage, pageSize } = tableState
    const params = {
      ...unref(searchParams)
    }
    if (
      !!params.createdOn &&
      Array.isArray(params.createdOn.value) &&
      params.createdOn.value.length > 1
    ) {
      if (!params.createdOn.value[0] && !!params.createdOn.value[1]) {
        params.createdOn.value[0] = '1900-01-01'
      } else if (!params.createdOn.value[1] && !!params.createdOn.value[0]) {
        params.createdOn.value[1] = '2099-12-31'
      }
    }
    const res = await getListApi({
      query: params,
      page: { pageNo: unref(currentPage), pageSize: unref(pageSize) },
      sort: unref(sortParams)
    })
    const { data } = res
    return {
      list: data.data,
      total: data.total
    }
  }
})
const { total, currentPage, pageSize, loading, dataList } = tableState
const { getList, refresh } = tableMethods
const selectRows = ref<Problem[]>([])
const setSelectionChange = (rows: Problem[]) => {
  selectRows.value = rows
}
watch(
  () => drawerVisible.value,
  (visible) => {
    if (!visible) {
      refresh()
    }
  }
)
const searchForm = ref(null)
useResizeObserver(searchForm, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const { height: pHeight = 0 } =
    entry.target.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
const contentRef = ref(null)
useResizeObserver(contentRef, () => {
  const ele = unref(searchForm) as unknown as HTMLDivElement
  const { height } = ele.getBoundingClientRect() ?? {}
  const { height: pHeight = 0 } = ele.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
</script>

<template>
  <ContentWrap ref="contentRef" :bodyStyle="{ height: 'calc(100vh - 8.25rem)' }">
    <div ref="searchForm">
      <Search
        :schema="allSchemas?.searchSchema"
        @search="setSearchParams"
        @reset="setSearchParams"
      />
    </div>
    <div class="mb-10px">
      <BaseButton
        type="primary"
        v-hasPermi="'task:process'"
        :disabled="
          selectRows.length !== 1 ||
          !selectRows[0].reasons
            .filter(
              (reason) =>
                !!reason.configs.find((config) => config.ownerId === userStore.userInfo?.id)
            )
            .some((reason) => {
              const statusIdxes = reason.configs
                .filter(
                  (config) =>
                    config.ownerId === userStore.userInfo?.id &&
                    (config.state === NodeState.ANALYZE || config.state === NodeState.VALIDATE)
                )
                .map((config) => config.stateIdx ?? 0)
              return statusIdxes.includes(reason.stateIdx)
            })
        "
        @click="action(selectRows[0], 'edit')"
      >
        {{ t('处理') }}
      </BaseButton>
    </div>

    <Table
      align="center"
      headerAlign="center"
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      @sort-change="setSortChange"
      @selection-change="setSelectionChange"
      :pagination="{ total }"
      :columns="allSchemas?.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      @refresh="refresh"
    />
    <Drawer v-model="drawerVisible" :title="t('原因分析')" :fullscreen="false" default-fullscreen>
      <Write :action-type="actionType" :form-schema="formSchemas" :current-row="currentRow" />

      <template #footer>
        <BaseButton @click="drawerVisible = false">{{ t('关闭') }}</BaseButton>
      </template>
    </Drawer>
  </ContentWrap>
</template>
