import ExcelJS, { Borders, Cell, Style } from 'exceljs'
import { saveAs } from 'file-saver'

export type Header = { header: string; key: string }[]

export function getDefaultHeaderCellStyle(): Partial<Style> {
  return {
    font: {
      size: 11,
      family: 1
    },
    fill: {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFB8CDE4' }
    },
    alignment: {
      horizontal: 'center',
      vertical: 'middle'
    }
  }
}

export function getDefaultCellStyle(): Partial<Style> {
  return {
    font: {
      size: 11,
      family: 1
    },
    alignment: {
      horizontal: 'center',
      vertical: 'middle'
    }
  }
}

export function countChineseCharacters(str: string) {
  const matches = str.match(/[\u4e00-\u9fa5]/g)
  return matches ? matches.length : 0
}

export function getDefaultBorder(): Partial<Borders> {
  return {
    top: { style: 'thin', color: { argb: 'FF000000' } },
    left: { style: 'thin', color: { argb: 'FF000000' } },
    bottom: { style: 'thin', color: { argb: 'FF000000' } },
    right: { style: 'thin', color: { argb: 'FF000000' } }
  }
}
export default class Exporter {
  private readonly _workbook: ExcelJS.Workbook

  constructor(
    props: {
      creator?: string
      lastModifiedBy?: string
      created?: Date
      modified?: Date
    } = {}
  ) {
    this._workbook = new ExcelJS.Workbook()
    const { creator, lastModifiedBy, created, modified } = props
    if (creator) {
      this.workbook.creator = creator
    }
    if (lastModifiedBy) {
      this.workbook.lastModifiedBy = lastModifiedBy
    }
    if (created) {
      this.workbook.created = created
    }
    if (modified) {
      this.workbook.modified = modified
    }
  }

  async addSheet<T>(options: {
    name?: string
    header?: Header
    data?: T[]
    setCellStyle?: (cell: Cell, rowNum: number, colNum: number) => Promise<any> | any
    autoFit?: boolean
  }) {
    const { name: sheetName = 'Sheet1', header, data = [], setCellStyle, autoFit = true } = options
    const sheet = this.workbook.addWorksheet(sheetName)
    if (header) {
      sheet.columns = header
      sheet.eachRow((row) => {
        row.eachCell((cell) => {
          cell.style = {
            ...getDefaultHeaderCellStyle(),
            border: getDefaultBorder()
          }
        })
      })
    }

    const setCell = async (cell: Cell, colNum: number, rowNum: number) => {
      cell.style = getDefaultCellStyle()
      if (setCellStyle) {
        await setCellStyle(cell, rowNum, colNum)
      }
    }

    for (const datum of data) {
      const row = sheet.addRow(datum)
      row.eachCell((cell, colNumber) => {
        setCell(cell, colNumber, row.number)
      })
    }
    if (autoFit) {
      ;(sheet.columns ?? []).forEach((column) => {
        let maxWidth = 12
        for (const value of column.values ?? []) {
          if (value !== null && typeof value !== 'undefined') {
            const values = value.toString().split('\n')
            for (const val of values) {
              const chCount = countChineseCharacters(val)
              maxWidth = Math.max(chCount * 2 + val.length - chCount, maxWidth)
            }
          }
        }
        column.width = maxWidth
      })
    }
    return sheet
  }

  async doExport(fileName: string) {
    const buffer = await this.workbook.xlsx.writeBuffer()
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    saveAs(blob, fileName)
  }

  public get workbook(): ExcelJS.Workbook {
    return this._workbook
  }
}
