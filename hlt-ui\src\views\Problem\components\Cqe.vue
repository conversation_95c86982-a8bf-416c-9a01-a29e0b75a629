<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { getOptionsByCategoriesApi } from '@/api/dictionary'
import { DictionaryCategory } from '@/api/dictionary/types'
import {
  deleteApi,
  deleteImageApi,
  getListApi,
  getPreviewUR<PERSON>pi,
  saveOrUpdateApi,
  submitApi,
  uploadApi
} from '@/api/problem'
import { Problem, ProblemStatus } from '@/api/problem/types'
import { BaseButton } from '@/components/Button'
import { ContentWrap } from '@/components/ContentWrap'
import { Drawer } from '@/components/Drawer'
import { ComponentNameEnum, FormSchema } from '@/components/Form'
import { Icon } from '@/components/Icon'
import { Search } from '@/components/Search'
import { Table, TableColumn } from '@/components/Table'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { convertRem } from '@/rem'
import { useLocaleStore } from '@/store/modules/locale'
import { useUserStore } from '@/store/modules/user'
import { Operators, omitNil } from '@/utils'
import { useResizeObserver } from '@vueuse/core'
import dayjs from 'dayjs'
import {
  ElCol,
  ElDialog,
  ElImage,
  ElMessage,
  ElPopconfirm,
  ElRow,
  ElSpace,
  UploadFile
} from 'element-plus'
import Uploader from './Uploader.vue'
import Write from './write/Index.vue'

const localeStore = useLocaleStore()
const { t } = useI18n()
const drawerVisible = ref(false)
const goodVisible = ref(false)
const badVisible = ref(false)
const currentRow = ref<Partial<Problem> | null>(null)
const actionType = ref('')
const action = async (row: Partial<Problem> | null, type: string) => {
  actionType.value = type
  if (type === 'add') {
    const { data } = await saveOrUpdateApi({ createdOn: dayjs().format('YYYY-MM-DD') } as Problem)
    currentRow.value = { id: data.id, status: data.status, createdOn: data.createdOn }
  } else {
    currentRow.value = omitNil(row ?? {})
  }
  drawerVisible.value = true
}
const searchParams = ref({})
const sortParams = ref<{ prop: string; order: 'ascending' | 'descending' | null } | undefined>({
  prop: 'createdOn',
  order: 'descending'
})
const setSearchParams = (params: any) => {
  searchParams.value = params
  getList()
}
const allSchemas = ref<Recordable>()
const formSchemas = ref<FormSchema[]>()
const userStore = useUserStore()
onBeforeMount(async () => {
  const { data } = await getOptionsByCategoriesApi([
    DictionaryCategory.BUSINESS_UNIT,
    DictionaryCategory.CUSTOMER,
    DictionaryCategory.FACTORY,
    DictionaryCategory.PRODUCT_LINE,
    DictionaryCategory.PRODUCT_STEP,
    DictionaryCategory.MACHINE_TYPE,
    DictionaryCategory.MACHINE_CATEGORY
  ])
  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection',
      type: 'selection',
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      }
    },
    {
      field: 'index',
      label: t('序号'),
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      },
      table: {
        slots: {
          default: ({ $index }: { $index: number }) => {
            const currentPage = tableState.currentPage.value || 1
            const pageSize = tableState.pageSize.value || 10
            const seq = (currentPage - 1) * pageSize + $index + 1
            return <span>{seq}</span>
          }
        }
      }
    },
    {
      field: 'code',
      label: t('问题编号'),
      table: {
        width: 180,
        sortable: 'custom',
        showOverflowTooltip: true
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      },
      detail: { hidden: true }
    },
    {
      field: 'createdOn',
      label: t('创建日期'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: ComponentNameEnum.CT_DATE,
        componentProps: {
          type: 'date',
          operate: Operators.BETWEEN,
          valueFormat: 'YYYY-MM-DD'
        }
      },
      detail: { hidden: true },
      formatter: (_: Recordable, __: TableColumn, cellValue) => {
        if (!cellValue) {
          return '-'
        }
        return dayjs(cellValue).format('YYYY-MM-DD')
      }
    },
    /*   {
      field: 'machineCategory',
      label: t('机种'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          filterable: true,
          options: (data[DictionaryCategory.MACHINE_CATEGORY] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          })),
          collapseTags: true
        }
      },
      detail: {
        span: 24
      },
      formatter: (_: Recordable, __: TableColumn, cellValue) => {
        return t(cellValue)
      }
    }, */
    {
      field: 'machineType',
      label: t('机型'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          filterable: true,
          options: (data[DictionaryCategory.MACHINE_TYPE] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          })),
          collapseTags: true
        }
      },
      detail: {
        span: 24
      },
      formatter: (_: Recordable, __: TableColumn, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'customer',
      label: t('客户'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          filterable: true,
          options: (data[DictionaryCategory.CUSTOMER] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          })),
          collapseTags: true
        }
      },
      formatter: (_: Recordable, __: TableColumn, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'projectCode',
      label: t('项目编号'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'workOrderCode',
      label: t('工单号'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'workOrderNum',
      label: t('工单数量'),
      table: {
        sortable: 'custom'
      },
      search: { hidden: true }
    },
    {
      field: 'factory',
      label: t('和而泰工厂'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          filterable: true,
          options: (data[DictionaryCategory.FACTORY] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          })),
          collapseTags: true
        }
      },
      formatter: (_: Recordable, __: TableColumn, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'businessUnit',
      label: t('事业部'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.BUSINESS_UNIT] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_: Recordable, __: TableColumn, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'creatorName',
      label: t('创建人'),
      table: {
        sortable: 'custom'
      },
      search: {
        hidden: true
      }
    },
    {
      field: 'productStep',
      label: t('产品阶段'),
      table: {
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.PRODUCT_STEP] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_: Recordable, __: TableColumn, cellValue) => {
        return t(cellValue)
      }
    }
    /* {
      field: 'action',
      label: t('操作'),
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      },
      table: {
        width: 100,
        fixed: 'right',
        slots: {
          default: (data: any) => {
            return (
              <>
                <BaseButton
                  type="text"
                  v-hasPermi="problem:edit"
                  disabled={userStore.userInfo?.id !== data.row.creatorId}
                  onClick={() => action(data.row, 'edit')}
                >
                  {t('编辑')}
                </BaseButton>
              </>
            )
          }
        }
      }
    } */
  ])
  allSchemas.value = useCrudSchemas(crudSchemas).allSchemas
  formSchemas.value = [
    {
      field: 'createdOn',
      label: t('创建日期'),
      component: ComponentNameEnum.DATE_PICKER,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        onChange: async (val) => {
          if (!!val?.length) {
            await save(true)
          }
        }
      }
    },
    {
      field: 'machineType',
      label: t('机型'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        onChange: async (val) => {
          if (!!val?.length) {
            await save(true)
          }
        },
        options: (data[DictionaryCategory.MACHINE_TYPE] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'customer',
      label: t('客户'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        onChange: async (val) => {
          if (!!val?.length) {
            await save(true)
          }
        },
        options: (data[DictionaryCategory.CUSTOMER] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'businessUnit',
      label: t('事业部'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        onChange: async (val) => {
          if (!!val?.length) {
            await save(true)
          }
        },
        options: (data[DictionaryCategory.BUSINESS_UNIT] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'factory',
      label: t('和而泰工厂'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        onChange: async (val) => {
          if (!!val?.length) {
            await save(true)
          }
        },
        options: (data[DictionaryCategory.FACTORY] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'productLine',
      label: t('生产线'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        onChange: async (val) => {
          if (!!val?.length) {
            await save(true)
          }
        },
        options: (data[DictionaryCategory.PRODUCT_LINE] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'projectCode',
      label: t('项目编号'),
      component: ComponentNameEnum.INPUT,
      componentProps: {
        showWordLimit: true,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        maxlength: 100
      },
      colProps: { xl: 6, lg: 6, md: 6, span: 12 }
    },
    {
      field: 'workOrderCode',
      label: t('工单号'),
      component: ComponentNameEnum.INPUT,
      componentProps: {
        showWordLimit: true,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        maxlength: 100
      },
      colProps: { xl: 6, lg: 6, md: 6, span: 12 }
    },
    {
      field: 'workOrderNum',
      label: t('工单数量'),
      component: ComponentNameEnum.INPUT_NUMBER,
      componentProps: {
        onChange: async () => {
          await save(true)
        }
      },
      colProps: { xl: 6, lg: 6, md: 6, span: 12 }
    },
    {
      field: 'productStep',
      label: t('产品阶段'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        onChange: async (val) => {
          if (!!val?.length) {
            await save(true)
          }
        },
        options: (data[DictionaryCategory.PRODUCT_STEP] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'descriptionDivider',
      label: t('问题描述'),
      component: ComponentNameEnum.DIVIDER,
      colProps: { span: 24 }
    },
    {
      field: 'descriptions.what',
      label: localeStore.isEn ? 'What happened:' : 'What happened:\n发生了什么:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.why',
      label: localeStore.isEn ? 'Why is it an issue:' : 'Why is it an issue:\n为什么是一个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.where',
      label: localeStore.isEn
        ? 'Where was the issue detected:'
        : 'Where was the issue detected:\n在哪里发现这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.when',
      label: localeStore.isEn ? 'When detected:' : 'When detected:\n什么时候发现:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.who',
      label: localeStore.isEn
        ? 'Who detected the issue:'
        : 'Who detected the issue:\n谁发现这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.how_detected',
      label: localeStore.isEn
        ? 'How was the issue detected:'
        : 'How was the issue detected:\n怎么发现的这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.how_many',
      label: localeStore.isEn ? 'How many:' : 'How many:\n发现数量:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        onBlur: async (e) => {
          if (!!e.target.value?.length) {
            await save(true)
          }
        },
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'images',
      label: t('图片'),
      colProps: { span: 24 },
      formItemProps: {
        slots: {
          default: ({ formValue }: any) => {
            const { id, goodPartImages = [], badPartImages = [] } = formValue as Problem
            const _goodPartImages = goodPartImages === null ? [] : goodPartImages
            const _badPartImages = badPartImages === null ? [] : badPartImages
            return (
              <ElRow class="w-full">
                <ElCol span={12}>
                  <div class={'w-full border-1px border-solid border-transparent border-r-0'}>
                    <div
                      class={
                        'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-success-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                      }
                    >
                      {t('好件')}
                    </div>
                    <div class={'py-3px px-3px'}>
                      <div
                        class={
                          'py-7px px-7px border-3px border-solid border-[var(--el-color-success-light-5)]'
                        }
                      >
                        <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                          {_goodPartImages.map(({ key }) => {
                            const src = getPreviewURLApi(id, key, 'good')
                            return (
                              <div class={'group relative w-full h-296px flex'}>
                                <BaseButton
                                  link
                                  class="opacity-0 absolute right-0 top-0 z-99 group-hover:opacity-100"
                                  onClick={() => handlerFileRemove(id, key, 'good')}
                                >
                                  <Icon
                                    icon={'ant-design:delete-outlined'}
                                    class={'!text-[var(--el-color-error)]'}
                                  />
                                </BaseButton>
                                <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                              </div>
                            )
                          })}

                          <div
                            onClick={() => {
                              goodVisible.value = true
                            }}
                            class="el-upload el-upload--picture-card"
                          >
                            <Icon icon={'ant-design:plus-outlined'} />
                          </div>
                        </ElSpace>
                      </div>
                      <ElDialog
                        width={500}
                        close-on-press-escape={false}
                        v-model={goodVisible.value}
                        v-slots={{
                          header: () => (
                            <div
                              class={
                                'flex h-full font-bold items-center justify-center text-white w-full bg-[var(--el-color-success-light-5)]'
                              }
                            >
                              {t('好件')}
                            </div>
                          )
                        }}
                        center
                        destroy-on-close
                        append-to-body
                      >
                        <Uploader onSubmit={async (file) => handlerFileUpload(id, file, 'good')} />
                      </ElDialog>
                    </div>
                  </div>
                </ElCol>
                <ElCol span={12}>
                  <div class={'w-full border-1px border-solid border-transparent'}>
                    <div
                      class={
                        'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-error-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                      }
                    >
                      {t('坏件')}
                    </div>
                    <div class={'py-3px px-3px'}>
                      <div
                        class={
                          'py-7px px-7px border-3px border-solid border-[var(--el-color-error-light-5)]'
                        }
                      >
                        <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                          {_badPartImages.map(({ key }) => {
                            const src = getPreviewURLApi(id, key, 'bad')
                            return (
                              <div class={'group relative w-full h-296px flex'}>
                                <BaseButton
                                  link
                                  class="opacity-0 absolute right-0 top-0 z-99 group-hover:opacity-100"
                                  onClick={() => handlerFileRemove(id, key, 'bad')}
                                >
                                  <Icon
                                    icon={'ant-design:delete-outlined'}
                                    class={'!text-[var(--el-color-error)]'}
                                  />
                                </BaseButton>
                                <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                              </div>
                            )
                          })}
                          <div
                            onClick={() => {
                              badVisible.value = true
                            }}
                            class="el-upload el-upload--picture-card"
                          >
                            <Icon icon={'ant-design:plus-outlined'} />
                          </div>
                        </ElSpace>
                      </div>
                      <ElDialog
                        width={500}
                        close-on-press-escape={false}
                        v-model={badVisible.value}
                        v-slots={{
                          header: () => (
                            <div
                              class={
                                'flex h-full font-bold items-center justify-center text-white w-full bg-[var(--el-color-error-light-5)]'
                              }
                            >
                              {t('坏件')}
                            </div>
                          )
                        }}
                        center
                        destroy-on-close
                        append-to-body
                      >
                        <Uploader
                          good={false}
                          onSubmit={async (file) => handlerFileUpload(id, file, 'bad')}
                        />
                      </ElDialog>
                    </div>
                  </div>
                </ElCol>
              </ElRow>
            )
          }
        }
      }
    }
  ]
})

const handlerFileRemove = async (id: ID, key: string, type: 'good' | 'bad') => {
  const { data } = await deleteImageApi(id, key, type)
  if (!!data) {
    currentRow.value = omitNil(data ?? {})
  }
}
const handlerFileUpload = async (id: ID, file: UploadFile, type: 'good' | 'bad') => {
  const { data } = await uploadApi(id, file, type)
  if (!!data) {
    currentRow.value = omitNil(data ?? {})
    if (type === 'good') {
      goodVisible.value = false
    } else {
      badVisible.value = false
    }
  }
}
const setSortChange = ({
  prop,
  order
}: {
  prop: string
  order: 'ascending' | 'descending' | null
}) => {
  sortParams.value = order === null ? undefined : { prop, order }
  getList()
}

const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const { currentPage, pageSize } = tableState
    const params: Record<string, any> = {
      ...unref(searchParams),
      status: [ProblemStatus.CQE],
      cqeId: !!userStore.userInfo?.id ? [userStore.userInfo.id] : []
    }
    if (
      !!params.createdOn &&
      Array.isArray(params.createdOn.value) &&
      params.createdOn.value.length > 1
    ) {
      if (!params.createdOn.value[0] && !!params.createdOn.value[1]) {
        params.createdOn.value[0] = '1900-01-01'
      } else if (!params.createdOn.value[1] && !!params.createdOn.value[0]) {
        params.createdOn.value[1] = '2099-12-31'
      }
    }
    const res = await getListApi({
      query: params,
      page: { pageNo: unref(currentPage), pageSize: unref(pageSize) },
      sort: unref(sortParams)
    })
    const { data } = res
    return {
      list: data.data,
      total: data.total
    }
  }
})
const { total, currentPage, pageSize, loading, dataList } = tableState
const { getList, refresh } = tableMethods

const writeRef = ref<ComponentRef<typeof Write>>()
const saveLoading = ref(false)
const selectRows = ref<Problem[]>([])
const setSelectionChange = (rows: Problem[]) => {
  selectRows.value = rows
}
const deleteEntity = async () => {
  loading.value = true
  const rows = unref(selectRows)
  try {
    await deleteApi(rows.map((item) => item.id))
    ElMessage.success(t('删除成功'))
    selectRows.value = []
    getList()
  } catch {
  } finally {
    loading.value = false
  }
}
const save = async (skipValidate: boolean = false, close: boolean = false) => {
  const write = unref(writeRef)
  const formData = (await write?.submit(skipValidate)) as unknown as Problem
  if (formData) {
    if (skipValidate) {
      try {
        await saveOrUpdateApi(formData)
      } catch {}
      if (close) {
        drawerVisible.value = false
      }
    } else {
      saveLoading.value = true
      try {
        let res = await saveOrUpdateApi(formData)
        if (res) {
          res = await submitApi(formData.id)
          if (res) {
            ElMessage.success(t('提交完成'))
            drawerVisible.value = false
          }
        }
      } catch {
        ElMessage.error(t('保存失败'))
      } finally {
        saveLoading.value = false
      }
    }
  }
}
const searchForm = ref(null)
useResizeObserver(searchForm, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const { height: pHeight = 0 } =
    entry.target.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
const contentRef = ref(null)
useResizeObserver(contentRef, () => {
  const ele = unref(searchForm) as unknown as HTMLDivElement
  const { height } = ele.getBoundingClientRect() ?? {}
  const { height: pHeight = 0 } = ele.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
</script>

<template>
  <ContentWrap ref="contentRef" :bodyStyle="{ height: 'calc(100vh - 14.25rem)' }">
    <div ref="searchForm">
      <Search
        :schema="allSchemas?.searchSchema"
        @search="setSearchParams"
        @reset="setSearchParams"
      />
    </div>
    <div class="mb-10px">
      <BaseButton
        type="primary"
        v-hasPermi="'problem:edit'"
        :disabled="selectRows.length !== 1"
        @click="action(selectRows[0], 'edit')"
      >
        {{ t('编辑') }}
      </BaseButton>
      <!-- <ElPopconfirm :title="t('是否确认删除')" @confirm="deleteEntity" :width="200">
        <template #reference>
          <BaseButton :loading="loading" v-hasPermi="'problem:delete'" type="danger" :disabled="!selectRows.length">
            {{ t('删除') }}
          </BaseButton>
        </template>
</ElPopconfirm> -->
    </div>

    <Table
      align="center"
      headerAlign="center"
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      @sort-change="setSortChange"
      @selection-change="setSelectionChange"
      :pagination="{ total }"
      :columns="allSchemas?.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      @refresh="refresh"
    />
    <Drawer
      v-model="drawerVisible"
      :fullscreen="false"
      :title="t('分配问题')"
      default-fullscreen
      @close="refresh"
    >
      <Write
        v-if="actionType !== 'detail'"
        :draft="false"
        :cqe="true"
        ref="writeRef"
        :form-schema="formSchemas"
        :current-row="currentRow"
      />

      <template #footer>
        <BaseButton
          v-if="actionType !== 'detail'"
          type="primary"
          :loading="saveLoading"
          @click="save(true, true)"
        >
          {{ t('保存') }}
        </BaseButton>
        <ElPopconfirm :title="t('是否确认提交')" @confirm="save(false, true)" :width="200">
          <template #reference>
            <BaseButton type="primary">
              {{ t('提交') }}
            </BaseButton>
          </template>
        </ElPopconfirm>
      </template>
    </Drawer>
  </ContentWrap>
</template>
