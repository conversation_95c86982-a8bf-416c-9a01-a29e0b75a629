<script lang="ts" setup>
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { propTypes } from '@/utils/propTypes'
import { ElTag, ElCol, ElFormItem, ElInput, ElRow, ElSpace, ElTree } from 'element-plus'
import type Node from 'element-plus/es/components/tree/src/model/node'
import { cloneDeep } from 'lodash-es'
import { ref, watch } from 'vue'
interface Tree {
  _id: string
  label: string
  editing: boolean
  error?: string
  children?: Tree[]
}

const { t } = useI18n()
const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('tree')

const props = defineProps({
  modelValue: propTypes.array.def([]),
  labelField: propTypes.string.def('')
})

const dataSource = ref<Tree[]>(
  cloneDeep(props.modelValue).map((item, idx) => loop(item as Tree, idx))
)

watch(
  () => props.modelValue,
  (val: any[]) => {
    dataSource.value = cloneDeep(val).map((item, idx) => loop(item as Tree, idx))
  }
)
const loop = (node: Tree, idx: number, parent?: Tree) => {
  node._id = `${parent?._id ?? 'root'}-${idx}`
  if (!!node.children?.length) {
    node.children.forEach((child, i) => loop(child, i, node))
  }
  node.label = node[props.labelField]
  node.editing = false
  return node
}

const emit = defineEmits(['update:modelValue'])

const append = (node?: Node) => {
  if (!node) {
    dataSource.value.push({
      _id: `root-${dataSource.value.length}}`,
      label: '',
      editing: true,
      children: []
    })
  } else {
    node.data.children.push({
      _id: `${node.data.id}-${node.data.children.length}`,
      label: '',
      editing: true
    })
  }
}
const editing = (data: Tree, editing: boolean) => {
  data.editing = editing
  if (!editing) {
    const newData = dataSource.value.map((item) => {
      const newItem: Recordable = { [props.labelField]: item.label }
      if (!!item.children?.length) {
        newItem.children = item.children.map((child) => ({ [props.labelField]: child.label }))
      }
      return newItem
    })
    emit('update:modelValue', newData)
  }
}
const remove = (node: Node, data: Tree) => {
  const parent = node.parent
  const children: Tree[] = parent.data.children || parent.data
  const index = children.findIndex((d) => d._id === data._id)
  children.splice(index, 1)
  const newData = (node.level === 1 ? node.parent.data : node.parent.parent.data).map((item) => {
    const newItem: Recordable = { [props.labelField]: item.label }
    if (!!item.children?.length) {
      newItem.children = item.children.map((child) => ({ [props.labelField]: child.label }))
    }
    return newItem
  })
  emit('update:modelValue', newData)
}

const handleInput = (e: FocusEvent | KeyboardEvent, data: Tree) => {
  const value = (e.target as HTMLInputElement)?.value ?? ''
  if (!value.length) {
    data.error = '请填写节点标签'
    return
  } else if (!value.trim().length) {
    data.error = '节点标签不可以为空字符串'
    return
  }
  data.label = value
  data[props.labelField] = value
  editing(data, false)
}
</script>

<template>
  <div :class="[prefixCls]">
    <ElRow>
      <ElCol :span="24">
        <BaseButton type="default" class="w-full" @click="append">
          <Icon icon="ant-design:plus-outlined" class="mr-4px" />
          {{ t('增加一项') }}
        </BaseButton>
      </ElCol>
    </ElRow>
    <ElTree :data="dataSource" node-key="_id" :expand-on-click-node="false" default-expand-all>
      <template #default="{ node, data }: { node: Node; data: Tree }">
        <span :class="`${prefixCls}-tree-node`">
          <ElFormItem :error="data.error">
            <ElSpace>
              <ElInput
                v-if="data.editing"
                v-model="data[props.labelField]"
                size="small"
                @keyup.enter="(e: KeyboardEvent) => handleInput(e, data)"
                @blur="(e) => handleInput(e, data)"
              />
              <span v-else>{{ node.label }}</span>
              <ElTag>{{ t(node.label) }}</ElTag>
            </ElSpace>
          </ElFormItem>
          <ElSpace>
            <BaseButton v-if="node.level === 1" link @click="append(node)"
              ><template #icon><Icon icon="ant-design:plus-outlined" /></template
            ></BaseButton>
            <BaseButton link @click="editing(data, true)"
              ><template #icon><Icon icon="ant-design:edit-outlined" /></template
            ></BaseButton>
            <BaseButton link @click="remove(node, data)" type="danger"
              ><template #icon><Icon icon="ant-design:delete-outlined" /></template
            ></BaseButton>
          </ElSpace>
        </span>
      </template>
    </ElTree>
  </div>
</template>

<style lang="less" scoped>
@prefix-cls: ~'@{namespace}-tree';
.@{prefix-cls} {
  &-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
  }
}
</style>
