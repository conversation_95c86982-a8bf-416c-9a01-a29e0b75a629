import { defineStore } from 'pinia'
import { store } from '../index'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import en from 'element-plus/es/locale/lang/en'
import { useStorage } from '@/hooks/web/useStorage'
import { LocaleDropdownType } from '@/components/LocaleDropdown'
import { Lang } from '@/api/login/types'

const { getStorage, setStorage } = useStorage()

const elLocaleMap = {
  'zh-CN': zhCn,
  en: en
}
interface LocaleState {
  currentLocale: LocaleDropdownType
  localeMap: LocaleDropdownType[]
  data: Lang[]
}

export const useLocaleStore = defineStore('locales', {
  state: (): LocaleState => {
    return {
      currentLocale: {
        lang: getStorage('lang') || 'zh-CN',
        elLocale: elLocaleMap[getStorage('lang') || 'zh-CN']
      },
      // 多语言
      localeMap: [
        {
          lang: 'zh-C<PERSON>',
          name: '简体中文'
        },
        {
          lang: 'en',
          name: 'English'
        }
      ],
      data: []
    }
  },
  getters: {
    getCurrentLocale(): LocaleDropdownType {
      return this.currentLocale
    },
    getLocaleMap(): LocaleDropdownType[] {
      return this.localeMap
    },
    getData(): Lang[] {
      return this.data
    },
    isEn(): boolean {
      return this.currentLocale.lang === 'en'
    }
  },
  actions: {
    setCurrentLocale(localeMap: LocaleDropdownType) {
      // this.locale = Object.assign(this.locale, localeMap)
      this.currentLocale.lang = localeMap?.lang
      this.currentLocale.elLocale = elLocaleMap[localeMap?.lang]
      setStorage('lang', localeMap?.lang)
    },
    setData(data: Lang[]) {
      this.data = data
    },
    getItem(key: string) {
      if (this.currentLocale.lang === 'zh-CN') {
        return this.data
          .find((datum) => datum.code === 'zh')
          ?.langData.find((item) => item.label === key)
      } else {
        return this.data
          .find((datum) => datum.code === 'en')
          ?.langData.find((item) => item.label === key)
      }
    },
    getMessage(lang: LocaleType) {
      if (lang === 'zh-CN') {
        return this.data.find((datum) => datum.code === 'zh')?.message
      } else {
        return this.data.find((datum) => datum.code === 'en')?.message
      }
    }
  }
})

export const useLocaleStoreWithOut = () => {
  return useLocaleStore(store)
}
