<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { getOptionsByCategoriesApi } from '@/api/dictionary'
import { DictionaryCategory, DictionaryOption } from '@/api/dictionary/types'
import {
  deleteApi,
  deleteImageApi,
  downloadTplApi,
  exportListApi,
  exportPdfApi,
  getByCodeApi,
  getListApi,
  getPreviewURLApi,
  saveOrUpdateApi,
  sendEmailApi,
  uploadApi,
  uploadTplApi
} from '@/api/problem'
import {
  Problem,
  ProblemStatus,
  ProblemStatusOptions,
  ReasonStateOptions,
  ReasonStateSelectOptions
} from '@/api/problem/types'
import { BaseButton } from '@/components/Button'
import { ContentWrap } from '@/components/ContentWrap'
import { Drawer } from '@/components/Drawer'
import { ComponentNameEnum, FormSchema } from '@/components/Form'
import { Icon } from '@/components/Icon'
import { hasPermi } from '@/components/Permission'
import { Search } from '@/components/Search'
import { Table } from '@/components/Table'
import { CrudSchema, useCrudSchemas } from '@/hooks/web/useCrudSchemas'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { convertRem } from '@/rem'
import { useLocaleStore } from '@/store/modules/locale'
import { useUserStore } from '@/store/modules/user'
import { Operators, omitNil } from '@/utils'
import { useResizeObserver } from '@vueuse/core'
import {
  ElCol,
  ElDialog,
  ElImage,
  ElMessage,
  ElPopconfirm,
  ElRow,
  ElSpace,
  ElTag,
  UploadFile,
  UploadInstance,
  UploadProps,
  UploadRawFile,
  UploadUserFile,
  genFileId
} from 'element-plus'
import { saveAs } from 'file-saver'
import Uploader from './Uploader.vue'
import Write from './write/Index.vue'

const localeStore = useLocaleStore()
const { t } = useI18n()
const drawerVisible = ref(false)
const goodVisible = ref(false)
const badVisible = ref(false)
const currentRow = ref<Partial<Problem> | null>(null)
const actionType = ref('')
const action = (row: Partial<Problem> | null, type: string) => {
  actionType.value = type
  currentRow.value = row
  drawerVisible.value = true
}

onMounted(async () => {
  const fullHash = window.location.hash
  let search: string
  if (!!fullHash?.length) {
    const segements = fullHash.split('?')
    search = segements.slice(1).join('')
  } else {
    search = window.location.search
  }
  const hashParams = new URLSearchParams(search)
  const no = hashParams.get('no')
  if (no) {
    const { data } = await getByCodeApi(no)
    action(data, 'view')
  }
})

const searchParams = ref<Recordable>({})
const sortParams = ref<{ prop: string; order: 'ascending' | 'descending' | null } | undefined>({
  prop: 'createdOn',
  order: 'descending'
})
const setSearchParams = (params: any) => {
  searchParams.value = params
  getList()
}
const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const { currentPage, pageSize } = tableState
    const { status, ...rest } = unref(searchParams)
    const params: Record<string, any> = {
      ...rest,
      status:
        Array.isArray(status) && status.length > 0
          ? status
          : [ProblemStatus.CQE, ProblemStatus.NEW, ProblemStatus.CLOSED, ProblemStatus.PROCESSING]
    }
    if (
      !!params.createdOn &&
      Array.isArray(params.createdOn.value) &&
      params.createdOn.value.length > 1
    ) {
      if (!params.createdOn.value[0] && !!params.createdOn.value[1]) {
        params.createdOn.value[0] = '1900-01-01'
      } else if (!params.createdOn.value[1] && !!params.createdOn.value[0]) {
        params.createdOn.value[1] = '2099-12-31'
      }
    }
    const res = await getListApi({
      query: params,
      page: { pageNo: unref(currentPage), pageSize: unref(pageSize) },
      sort: unref(sortParams)
    })
    const { data } = res
    return {
      list: data.data,
      total: data.total
    }
  }
})
const { total, currentPage, pageSize, loading, dataList } = tableState

const dictionarys = ref<Record<string, DictionaryOption[]>>()
const allSchemas = ref<Recordable>()
const formSchemas = ref<FormSchema[]>()
const userStore = useUserStore()
onBeforeMount(async () => {
  const { data } = await getOptionsByCategoriesApi([
    DictionaryCategory.BUSINESS_UNIT,
    DictionaryCategory.CUSTOMER,
    DictionaryCategory.FACTORY,
    DictionaryCategory.PRODUCT_LINE,
    DictionaryCategory.PRODUCT_STEP,
    DictionaryCategory.MACHINE_TYPE,
    DictionaryCategory.MACHINE_CATEGORY
  ])
  dictionarys.value = data
  const crudSchemas = reactive<CrudSchema[]>([
    {
      field: 'selection',
      type: 'selection',
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      }
    },
    {
      field: 'index',
      label: t('序号'),
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      },
      table: {
        slots: {
          default: ({ $index }: { $index: number }) => {
            const currentPage = tableState.currentPage.value || 1
            const pageSize = tableState.pageSize.value || 10
            const seq = (currentPage - 1) * pageSize + $index + 1
            return <span>{seq}</span>
          }
        }
      }
    },
    {
      field: 'code',
      label: t('问题编号'),
      table: {
        width: 180,
        sortable: 'custom',
        showOverflowTooltip: true,
        formatter: (row, _, cellValue) => {
          if (hasPermi('problem:view')) {
            return (
              <BaseButton
                type="info"
                link
                onClick={() => action(row, 'view')}
                style={{ userSelect: 'text' }}
              >
                {cellValue}
              </BaseButton>
            )
          }
          return cellValue
        }
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      },
      detail: { hidden: true }
    },
    {
      field: 'createdOn',
      label: t('创建日期'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: ComponentNameEnum.CT_DATE,
        componentProps: {
          type: 'date',
          operate: Operators.BETWEEN,
          valueFormat: 'YYYY-MM-DD'
        }
      },
      detail: { hidden: true }
    },
    {
      field: 'machineType',
      label: t('机型'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.MACHINE_TYPE] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      detail: {
        span: 24
      },
      formatter: (_, __, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'customer',
      label: t('客户'),
      table: {
        width: 120,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.CUSTOMER] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'projectCode',
      label: t('项目编号'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'workOrderCode',
      label: t('工单号'),
      table: {
        width: 120,
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'workOrderNum',
      label: t('工单数量'),
      table: {
        width: 160,
        sortable: 'custom'
      },
      search: { hidden: true }
    },
    {
      field: 'factory',
      label: t('和而泰工厂'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.FACTORY] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'businessUnit',
      label: t('事业部'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.BUSINESS_UNIT] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'creatorName',
      label: t('创建人'),
      table: {
        width: 120,
        sortable: 'custom'
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    },
    {
      field: 'productStep',
      label: t('产品阶段'),
      table: {
        width: 140,
        sortable: 'custom'
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: (data[DictionaryCategory.PRODUCT_STEP] ?? []).map((item) => ({
            label: t(item.name),
            value: item.name
          }))
        }
      },
      formatter: (_, __, cellValue) => {
        return t(cellValue)
      }
    },
    {
      field: 'status',
      label: t('状态'),
      table: {
        width: 120,
        sortable: 'custom',
        formatter: (_, __, cellValue: string) => {
          const label = ProblemStatusOptions.find((item) => item.value === cellValue)?.label
          if (!!label) {
            return t(label)
          }
          return '-'
        }
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: ProblemStatusOptions.filter(({ value }) => value !== 'OBSOLETE').map(
            ({ label, value }) => ({ value, label: t(label) })
          )
        }
      }
    },
    {
      field: 'node',
      label: t('当前节点'),
      table: {
        width: 120,
        formatter: (entity: Problem) => {
          if (entity.reasons.length === 1) {
            return <ElTag>{t(ReasonStateOptions[entity.reasons[0].state])}</ElTag>
          }
          return (
            <ElSpace direction="vertical">
              {entity.reasons.map((reason) => {
                return <ElTag>{t(ReasonStateOptions[reason.state])}</ElTag>
              })}
            </ElSpace>
          )
        }
      },
      search: {
        component: 'Select',
        componentProps: {
          multiple: true,
          collapseTags: true,
          filterable: true,
          options: ReasonStateSelectOptions.map(({ label, value }) => ({ value, label: t(label) }))
        }
      }
    },
    {
      field: 'owner',
      label: t('责任人'),
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      },
      table: {
        minWidth: 150,
        formatter: (entity: Problem) => {
          if (entity.reasons.length === 1) {
            const reason = entity.reasons[0]
            return (
              <ElTag>
                {reason.configs.find((config) => config.state === reason.state)?.ownerName ?? '-'}
              </ElTag>
            )
          }

          return (
            <ElSpace direction="vertical">
              {entity.reasons.map((reason) => {
                return (
                  <ElTag>
                    {reason.configs.find((config) => config.state === reason.state)?.ownerName ??
                      '-'}
                  </ElTag>
                )
              })}
            </ElSpace>
          )
        }
      }
    },
    {
      field: 'nextNode',
      label: t('下一节点'),
      table: {
        minWidth: 250,
        formatter: (entity: Problem) => {
          if (entity.reasons.length === 1) {
            const current = entity.reasons[0].configs.find(
              (config) => config.state === entity.reasons[0].state
            )
            const next = entity.reasons[0].configs.find(
              (config) => config.stateIdx === (current?.stateIdx ?? -999) + 1
            )
            return (
              <ElTag>
                {!!next && !!next.state ? t(ReasonStateOptions[next.state]) : '-'}
                {!!next && !!next.ownerName?.length ? '[' + next.ownerName + ']' : ''}
              </ElTag>
            )
          }
          return (
            <ElSpace direction="vertical">
              {entity.reasons.map((reason) => {
                const current = reason.configs.find((config) => config.state === reason.state)
                const next = reason.configs.find(
                  (config) => config.stateIdx === (current?.stateIdx ?? -999) + 1
                )
                return (
                  <ElTag>
                    {!!next && !!next.state ? t(ReasonStateOptions[next.state]) : '-'}
                    {!!next && !!next.ownerName?.length ? '[' + next.ownerName + ']' : ''}
                  </ElTag>
                )
              })}
            </ElSpace>
          )
        }
      },
      search: {
        hidden: true
      }
    },
    {
      field: 'remark',
      label: t('备注'),
      table: {
        minWidth: 120,
        formatter: (entity: Problem) => {
          if (entity.reasons.length === 1) {
            const reason = entity.reasons[0]
            return <ElTag>{reason.remark ?? '-'}</ElTag>
          }
          return (
            <ElSpace direction="vertical">
              {entity.reasons.map((reason) => {
                return <ElTag>{reason.remark ?? '-'}</ElTag>
              })}
            </ElSpace>
          )
        }
      },
      search: {
        component: 'Input',
        componentProps: {
          operate: Operators.LIKE
        }
      }
    }
    /*  {
      field: 'action',
      width: 100,
      label: t('操作'),
      search: {
        hidden: true
      },
      form: {
        hidden: true
      },
      detail: {
        hidden: true
      },
      table: {
        fixed: 'right',
        slots: {
          default: (data: { row: Problem }) => {
            if (data.row.status === ProblemStatus.CLOSED) {
              return (
                <>
                  <BaseButton
                    type="text"
                    v-hasPermi="problem:view"
                    onClick={() => action(data.row, 'view')}
                  >
                    {t('查看')}
                  </BaseButton>
                </>
              )
            }
            return (
              <>
                <BaseButton
                  type="text"
                  v-hasPermi="problem:view"
                  onClick={() => action(data.row, 'view')}
                >
                  {t('查看')}
                </BaseButton>
                <BaseButton
                  type="text"
                  v-hasPermi="problem:edit"
                  disabled={userStore.userInfo?.id !== data.row.creatorId}
                  onClick={() => action(data.row, 'edit')}
                >
                  {t('编辑')}
                </BaseButton>
              </>
            )
          }
        }
      }
    } */
  ])
  allSchemas.value = useCrudSchemas(crudSchemas).allSchemas
  formSchemas.value = [
    {
      field: 'code',
      label: t('问题编号'),
      component: actionType.value === 'view' ? ComponentNameEnum.TEXT : ComponentNameEnum.INPUT,
      colProps: { xl: 3, lg: 3, md: 3, span: 12 },
      componentProps: { disabled: true, placeholder: t('问题编号由系统自动生成') }
    },
    {
      field: 'createdOn',
      label: t('创建日期'),
      component: ComponentNameEnum.DATE_PICKER,
      colProps: { xl: 3, lg: 3, md: 3, span: 12 },
      componentProps: { valueFormat: 'YYYY-MM-DD' }
    },
    {
      field: 'machineType',
      label: t('机型'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.MACHINE_TYPE] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'customer',
      label: t('客户'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.CUSTOMER] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'businessUnit',
      label: t('事业部'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.BUSINESS_UNIT] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'factory',
      label: t('和而泰工厂'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.FACTORY] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'productLine',
      label: t('生产线'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.PRODUCT_LINE] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'projectCode',
      label: t('项目编号'),
      component: ComponentNameEnum.INPUT,
      componentProps: {
        showWordLimit: true,
        maxlength: 100
      },
      colProps: { xl: 6, lg: 6, md: 6, span: 12 }
    },
    {
      field: 'workOrderCode',
      label: t('工单号'),
      component: ComponentNameEnum.INPUT,
      componentProps: {
        showWordLimit: true,
        maxlength: 100
      },
      colProps: { xl: 6, lg: 6, md: 6, span: 12 }
    },
    {
      field: 'workOrderNum',
      label: t('工单数量'),
      component: ComponentNameEnum.INPUT_NUMBER,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 }
    },
    {
      field: 'productStep',
      label: t('产品阶段'),
      component: ComponentNameEnum.SELECT,
      colProps: { xl: 6, lg: 6, md: 6, span: 12 },
      componentProps: {
        filterable: true,
        options: (data[DictionaryCategory.PRODUCT_STEP] ?? []).map((item) => ({
          label: t(item.name),
          value: item.name
        }))
      }
    },
    {
      field: 'descriptionDivider',
      label: t('问题描述'),
      component: ComponentNameEnum.DIVIDER,
      colProps: { span: 24 }
    },
    {
      field: 'descriptions.what',
      label: localeStore.isEn ? 'What happened:' : 'What happened:\n发生了什么:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.why',
      label: localeStore.isEn ? 'Why is it an issue:' : 'Why is it an issue:\n为什么是一个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.where',
      label: localeStore.isEn
        ? 'Where was the issue detected:'
        : 'Where was the issue detected:\n在哪里发现这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.when',
      label: localeStore.isEn ? 'When detected:' : 'When detected:\n什么时候发现:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.who',
      label: localeStore.isEn
        ? 'Who detected the issue:'
        : 'Who detected the issue:\n谁发现这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.how_detected',
      label: localeStore.isEn
        ? 'How was the issue detected:'
        : 'How was the issue detected:\n怎么发现的这个问题:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'descriptions.how_many',
      label: localeStore.isEn ? 'How many:' : 'How many:\n发现数量:',
      component: ComponentNameEnum.INPUT,
      colProps: { span: 8 },
      componentProps: {
        type: 'textarea',
        showWordLimit: true,
        maxlength: 500,
        autosize: { minRows: 4, maxRows: 6 }
      }
    },
    {
      field: 'images',
      label: t('图片'),
      colProps: { span: 24 },
      formItemProps: {
        slots: {
          default: ({ formValue }: any) => {
            const { id, goodPartImages = [], badPartImages = [] } = formValue as Problem
            const _goodPartImages = goodPartImages === null ? [] : goodPartImages
            const _badPartImages = badPartImages === null ? [] : badPartImages
            return (
              <ElRow class="w-full">
                <ElCol span={12}>
                  <div class={'w-full border-1px border-solid border-transparent border-r-0'}>
                    <div
                      class={
                        'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-success-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                      }
                    >
                      {t('好件')}
                    </div>
                    <div class={'py-3px px-3px'}>
                      <div
                        class={
                          'py-7px px-7px border-3px border-solid border-[var(--el-color-success-light-5)]'
                        }
                      >
                        <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                          {_goodPartImages.map(({ key }) => {
                            const src = getPreviewURLApi(id, key, 'good')
                            return (
                              <div class={'group relative w-full h-296px flex'}>
                                <BaseButton
                                  link
                                  class="opacity-0 absolute right-0 top-0 z-99 group-hover:opacity-100"
                                  onClick={() => handlerFileRemove(id, key, 'good')}
                                >
                                  <Icon
                                    icon={'ant-design:delete-outlined'}
                                    class={'!text-[var(--el-color-error)]'}
                                  />
                                </BaseButton>
                                <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                              </div>
                            )
                          })}

                          <div
                            onClick={() => {
                              goodVisible.value = true
                            }}
                            class="el-upload el-upload--picture-card"
                          >
                            <Icon icon={'ant-design:plus-outlined'} />
                          </div>
                        </ElSpace>
                      </div>
                      <ElDialog
                        width={500}
                        close-on-press-escape={false}
                        v-model={goodVisible.value}
                        v-slots={{
                          header: () => (
                            <div
                              class={
                                'flex h-full font-bold items-center justify-center text-white w-full bg-[var(--el-color-success-light-5)]'
                              }
                            >
                              {t('好件')}
                            </div>
                          )
                        }}
                        center
                        destroy-on-close
                        append-to-body
                      >
                        <Uploader onSubmit={async (file) => handlerFileUpload(id, file, 'good')} />
                      </ElDialog>
                    </div>
                  </div>
                </ElCol>
                <ElCol span={12}>
                  <div class={'w-full border-1px border-solid border-transparent'}>
                    <div
                      class={
                        'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-error-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                      }
                    >
                      {t('坏件')}
                    </div>
                    <div class={'py-3px px-3px'}>
                      <div
                        class={
                          'py-7px px-7px border-3px border-solid border-[var(--el-color-error-light-5)]'
                        }
                      >
                        <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                          {_badPartImages.map(({ key }) => {
                            const src = getPreviewURLApi(id, key, 'bad')
                            return (
                              <div class={'group relative w-full h-296px flex'}>
                                <BaseButton
                                  link
                                  class="opacity-0 absolute right-0 top-0 z-99 group-hover:opacity-100"
                                  onClick={() => handlerFileRemove(id, key, 'bad')}
                                >
                                  <Icon
                                    icon={'ant-design:delete-outlined'}
                                    class={'!text-[var(--el-color-error)]'}
                                  />
                                </BaseButton>
                                <ElImage fit={'contain'} lazy src={src} previewSrcList={[src]} />
                              </div>
                            )
                          })}
                          <div
                            onClick={() => {
                              badVisible.value = true
                            }}
                            class="el-upload el-upload--picture-card"
                          >
                            <Icon icon={'ant-design:plus-outlined'} />
                          </div>
                        </ElSpace>
                      </div>
                      <ElDialog
                        width={500}
                        close-on-press-escape={false}
                        v-model={badVisible.value}
                        v-slots={{
                          header: () => (
                            <div
                              class={
                                'flex h-full font-bold items-center justify-center text-white w-full bg-[var(--el-color-error-light-5)]'
                              }
                            >
                              {t('坏件')}
                            </div>
                          )
                        }}
                        center
                        destroy-on-close
                        append-to-body
                      >
                        <Uploader
                          good={false}
                          onSubmit={async (file) => handlerFileUpload(id, file, 'bad')}
                        />
                      </ElDialog>
                    </div>
                  </div>
                </ElCol>
              </ElRow>
            )
          }
        }
      }
    }
  ]
})

watch(
  () => actionType.value,
  (type) => {
    if (type === 'edit') {
      formSchemas.value = [
        {
          field: 'code',
          label: t('问题编号'),
          component: ComponentNameEnum.INPUT,
          colProps: { xl: 3, lg: 3, md: 3, span: 12 },
          componentProps: { disabled: true, placeholder: t('问题编号由系统自动生成') }
        },
        {
          field: 'createdOn',
          label: t('创建日期'),
          component: ComponentNameEnum.DATE_PICKER,
          colProps: { xl: 3, lg: 3, md: 3, span: 12 },
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
            onBlur: (e) => {
              console.log(e.target.value, 111)
            }
          }
        },
        {
          field: 'machineType',
          label: t('机型'),
          component: ComponentNameEnum.SELECT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 },
          componentProps: {
            filterable: true,
            options: (dictionarys.value?.[DictionaryCategory.MACHINE_TYPE] ?? []).map((item) => ({
              label: t(item.name),
              value: item.name
            }))
          }
        },
        {
          field: 'customer',
          label: t('客户'),
          component: ComponentNameEnum.SELECT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 },
          componentProps: {
            filterable: true,
            options: (dictionarys.value?.[DictionaryCategory.CUSTOMER] ?? []).map((item) => ({
              label: t(item.name),
              value: item.name
            }))
          }
        },
        {
          field: 'businessUnit',
          label: t('事业部'),
          component: ComponentNameEnum.SELECT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 },
          componentProps: {
            filterable: true,
            options: (dictionarys.value?.[DictionaryCategory.BUSINESS_UNIT] ?? []).map((item) => ({
              label: t(item.name),
              value: item.name
            }))
          }
        },
        {
          field: 'factory',
          label: t('和而泰工厂'),
          component: ComponentNameEnum.SELECT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 },
          componentProps: {
            filterable: true,
            options: (dictionarys.value?.[DictionaryCategory.FACTORY] ?? []).map((item) => ({
              label: t(item.name),
              value: item.name
            }))
          }
        },
        {
          field: 'productLine',
          label: t('生产线'),
          component: ComponentNameEnum.SELECT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 },
          componentProps: {
            filterable: true,
            options: (dictionarys.value?.[DictionaryCategory.PRODUCT_LINE] ?? []).map((item) => ({
              label: t(item.name),
              value: item.name
            }))
          }
        },
        {
          field: 'projectCode',
          label: t('项目编号'),
          component: ComponentNameEnum.INPUT,
          componentProps: {
            showWordLimit: true,
            maxlength: 100
          },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'workOrderCode',
          label: t('工单号'),
          component: ComponentNameEnum.INPUT,
          componentProps: {
            showWordLimit: true,
            maxlength: 100
          },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'workOrderNum',
          label: t('工单数量'),
          component: ComponentNameEnum.INPUT_NUMBER,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'productStep',
          label: t('产品阶段'),
          component: ComponentNameEnum.SELECT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 },
          componentProps: {
            filterable: true,
            options: (dictionarys.value?.[DictionaryCategory.PRODUCT_STEP] ?? []).map((item) => ({
              label: t(item.name),
              value: item.name
            }))
          }
        },
        {
          field: 'descriptionDivider',
          label: t('问题描述'),
          component: ComponentNameEnum.DIVIDER,
          colProps: { span: 24 }
        },
        {
          field: 'descriptions.what',
          label: localeStore.isEn ? 'What happened:' : 'What happened:\n发生了什么:',
          component: ComponentNameEnum.INPUT,
          colProps: { span: 8 },
          componentProps: {
            type: 'textarea',
            showWordLimit: true,
            maxlength: 500,
            autosize: { minRows: 4, maxRows: 6 }
          }
        },
        {
          field: 'descriptions.why',
          label: localeStore.isEn
            ? 'Why is it an issue:'
            : 'Why is it an issue:\n为什么是一个问题:',
          component: ComponentNameEnum.INPUT,
          colProps: { span: 8 },
          componentProps: {
            type: 'textarea',
            showWordLimit: true,
            maxlength: 500,
            autosize: { minRows: 4, maxRows: 6 }
          }
        },
        {
          field: 'descriptions.where',
          label: localeStore.isEn
            ? 'Where was the issue detected:'
            : 'Where was the issue detected:\n在哪里发现这个问题:',
          component: ComponentNameEnum.INPUT,
          colProps: { span: 8 },
          componentProps: {
            type: 'textarea',
            showWordLimit: true,
            maxlength: 500,
            autosize: { minRows: 4, maxRows: 6 }
          }
        },
        {
          field: 'descriptions.when',
          label: localeStore.isEn ? 'When detected:' : 'When detected:\n什么时候发现:',
          component: ComponentNameEnum.INPUT,
          colProps: { span: 8 },
          componentProps: {
            type: 'textarea',
            showWordLimit: true,
            maxlength: 500,
            autosize: { minRows: 4, maxRows: 6 }
          }
        },
        {
          field: 'descriptions.who',
          label: localeStore.isEn
            ? 'Who detected the issue:'
            : 'Who detected the issue:\n谁发现这个问题:',
          component: ComponentNameEnum.INPUT,
          colProps: { span: 8 },
          componentProps: {
            type: 'textarea',
            showWordLimit: true,
            maxlength: 500,
            autosize: { minRows: 4, maxRows: 6 }
          }
        },
        {
          field: 'descriptions.how_detected',
          label: localeStore.isEn
            ? 'How was the issue detected:'
            : 'How was the issue detected:\n怎么发现的这个问题:',
          component: ComponentNameEnum.INPUT,
          colProps: { span: 8 },
          componentProps: {
            type: 'textarea',
            showWordLimit: true,
            maxlength: 500,
            autosize: { minRows: 4, maxRows: 6 }
          }
        },
        {
          field: 'descriptions.how_many',
          label: localeStore.isEn ? 'How many:' : 'How many:\n发现数量:',
          component: ComponentNameEnum.INPUT,
          colProps: { span: 8 },
          componentProps: {
            type: 'textarea',
            showWordLimit: true,
            maxlength: 500,
            autosize: { minRows: 4, maxRows: 6 }
          }
        },
        {
          field: 'images',
          label: t('图片'),
          colProps: { span: 24 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                const { id, goodPartImages = [], badPartImages = [] } = formValue as Problem
                const _goodPartImages = goodPartImages === null ? [] : goodPartImages
                const _badPartImages = badPartImages === null ? [] : badPartImages
                return (
                  <ElRow class="w-full">
                    <ElCol span={12}>
                      <div class={'w-full border-1px border-solid border-transparent border-r-0'}>
                        <div
                          class={
                            'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-success-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                          }
                        >
                          {t('好件')}
                        </div>
                        <div class={'py-3px px-3px'}>
                          <div
                            class={
                              'py-7px px-7px border-3px border-solid border-[var(--el-color-success-light-5)]'
                            }
                          >
                            <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                              {_goodPartImages.map(({ key }) => {
                                const src = getPreviewURLApi(id, key, 'good')
                                return (
                                  <div class={'group relative w-full h-296px flex'}>
                                    <BaseButton
                                      link
                                      class="opacity-0 absolute right-0 top-0 z-99 group-hover:opacity-100"
                                      onClick={() => handlerFileRemove(id, key, 'good')}
                                    >
                                      <Icon
                                        icon={'ant-design:delete-outlined'}
                                        class={'!text-[var(--el-color-error)]'}
                                      />
                                    </BaseButton>
                                    <ElImage
                                      fit={'contain'}
                                      lazy
                                      src={src}
                                      previewSrcList={[src]}
                                    />
                                  </div>
                                )
                              })}

                              <div
                                onClick={() => {
                                  goodVisible.value = true
                                }}
                                class="el-upload el-upload--picture-card"
                              >
                                <Icon icon={'ant-design:plus-outlined'} />
                              </div>
                            </ElSpace>
                          </div>
                          <ElDialog
                            width={500}
                            close-on-press-escape={false}
                            v-model={goodVisible.value}
                            v-slots={{
                              header: () => (
                                <div
                                  class={
                                    'flex h-full font-bold items-center justify-center text-white w-full bg-[var(--el-color-success-light-5)]'
                                  }
                                >
                                  {t('好件')}
                                </div>
                              )
                            }}
                            center
                            destroy-on-close
                            append-to-body
                          >
                            <Uploader
                              onSubmit={async (file) => handlerFileUpload(id, file, 'good')}
                            />
                          </ElDialog>
                        </div>
                      </div>
                    </ElCol>
                    <ElCol span={12}>
                      <div class={'w-full border-1px border-solid border-transparent'}>
                        <div
                          class={
                            'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-error-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                          }
                        >
                          {t('坏件')}
                        </div>
                        <div class={'py-3px px-3px'}>
                          <div
                            class={
                              'py-7px px-7px border-3px border-solid border-[var(--el-color-error-light-5)]'
                            }
                          >
                            <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                              {_badPartImages.map(({ key }) => {
                                const src = getPreviewURLApi(id, key, 'bad')
                                return (
                                  <div class={'group relative w-full h-296px flex'}>
                                    <BaseButton
                                      link
                                      class="opacity-0 absolute right-0 top-0 z-99 group-hover:opacity-100"
                                      onClick={() => handlerFileRemove(id, key, 'bad')}
                                    >
                                      <Icon
                                        icon={'ant-design:delete-outlined'}
                                        class={'!text-[var(--el-color-error)]'}
                                      />
                                    </BaseButton>
                                    <ElImage
                                      fit={'contain'}
                                      lazy
                                      src={src}
                                      previewSrcList={[src]}
                                    />
                                  </div>
                                )
                              })}
                              <div
                                onClick={() => {
                                  badVisible.value = true
                                }}
                                class="el-upload el-upload--picture-card"
                              >
                                <Icon icon={'ant-design:plus-outlined'} />
                              </div>
                            </ElSpace>
                          </div>
                          <ElDialog
                            width={500}
                            close-on-press-escape={false}
                            v-model={badVisible.value}
                            v-slots={{
                              header: () => (
                                <div
                                  class={
                                    'flex h-full font-bold items-center justify-center text-white w-full bg-[var(--el-color-error-light-5)]'
                                  }
                                >
                                  {t('坏件')}
                                </div>
                              )
                            }}
                            center
                            destroy-on-close
                            append-to-body
                          >
                            <Uploader
                              good={false}
                              onSubmit={async (file) => handlerFileUpload(id, file, 'bad')}
                            />
                          </ElDialog>
                        </div>
                      </div>
                    </ElCol>
                  </ElRow>
                )
              }
            }
          }
        }
      ]
    } else if (type === 'view') {
      formSchemas.value = [
        {
          field: 'code',
          label: t('问题编号'),
          component: ComponentNameEnum.TEXT,
          colProps: { xl: 3, lg: 3, md: 3, span: 12 }
        },
        {
          field: 'createdOn',
          label: t('创建日期'),
          component: ComponentNameEnum.TEXT,
          colProps: { xl: 3, lg: 3, md: 3, span: 12 }
        },
        {
          field: 'machineType',
          label: t('机型'),
          component: ComponentNameEnum.TEXT,
          componentProps: { i18n: true },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'customer',
          label: t('客户'),
          component: ComponentNameEnum.TEXT,
          componentProps: { i18n: true },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'businessUnit',
          label: t('事业部'),
          component: ComponentNameEnum.TEXT,
          componentProps: { i18n: true },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'factory',
          label: t('和而泰工厂'),
          component: ComponentNameEnum.TEXT,
          componentProps: { i18n: true },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'productLine',
          label: t('生产线'),
          component: ComponentNameEnum.TEXT,
          componentProps: { i18n: true },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'projectCode',
          label: t('项目编号'),
          component: ComponentNameEnum.TEXT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'workOrderCode',
          label: t('工单号'),
          component: ComponentNameEnum.TEXT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'workOrderNum',
          label: t('工单数量'),
          component: ComponentNameEnum.TEXT,
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'productStep',
          label: t('产品阶段'),
          component: ComponentNameEnum.TEXT,
          componentProps: { i18n: true },
          colProps: { xl: 6, lg: 6, md: 6, span: 12 }
        },
        {
          field: 'descriptionDivider',
          label: t('问题描述'),
          component: ComponentNameEnum.DIVIDER,
          colProps: { span: 24 }
        },
        {
          field: 'descriptions.what',
          label: localeStore.isEn ? 'What happened:' : 'What happened:\n发生了什么:',
          colProps: { span: 8 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                return (
                  <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                    {formValue.descriptions?.what}
                  </p>
                )
              }
            }
          }
        },
        {
          field: 'descriptions.why',
          label: localeStore.isEn
            ? 'Why is it an issue:'
            : 'Why is it an issue:\n为什么是一个问题:',
          colProps: { span: 8 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                return (
                  <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                    {formValue.descriptions?.why}
                  </p>
                )
              }
            }
          }
        },
        {
          field: 'descriptions.where',
          label: localeStore.isEn
            ? 'Where was the issue detected:'
            : 'Where was the issue detected:\n在哪里发现这个问题:',
          colProps: { span: 8 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                return (
                  <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                    {formValue.descriptions?.where}
                  </p>
                )
              }
            }
          }
        },
        {
          field: 'descriptions.when',
          label: localeStore.isEn ? 'When detected:' : 'When detected:\n什么时候发现:',
          colProps: { span: 8 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                return (
                  <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                    {formValue.descriptions?.when}
                  </p>
                )
              }
            }
          }
        },
        {
          field: 'descriptions.who',
          label: localeStore.isEn
            ? 'Who detected the issue:'
            : 'Who detected the issue:\n谁发现这个问题:',
          colProps: { span: 8 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                return (
                  <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                    {formValue.descriptions?.who}
                  </p>
                )
              }
            }
          }
        },
        {
          field: 'descriptions.how_detected',
          label: localeStore.isEn
            ? 'How was the issue detected:'
            : 'How was the issue detected:\n怎么发现的这个问题:',
          colProps: { span: 8 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                return (
                  <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                    {formValue.descriptions?.how_detected}
                  </p>
                )
              }
            }
          }
        },
        {
          field: 'descriptions.how_many',
          label: localeStore.isEn ? 'How many:' : 'How many:\n发现数量:',
          colProps: { span: 8 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                return (
                  <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                    {formValue.descriptions?.how_many}
                  </p>
                )
              }
            }
          }
        },
        {
          field: 'images',
          label: t('图片'),
          colProps: { span: 24 },
          formItemProps: {
            slots: {
              default: ({ formValue }: any) => {
                const { id, goodPartImages = [], badPartImages = [] } = formValue as Problem
                const _goodPartImages = goodPartImages === null ? [] : goodPartImages
                const _badPartImages = badPartImages === null ? [] : badPartImages
                return (
                  <ElRow class="w-full">
                    <ElCol span={12}>
                      <div class={'w-full border-1px border-solid border-transparent border-r-0'}>
                        <div
                          class={
                            'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-success-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                          }
                        >
                          {t('好件')}
                        </div>
                        <div class={'py-3px px-3px'}>
                          <div
                            class={
                              'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-success-light-5)]'
                            }
                          >
                            <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                              {_goodPartImages.map(({ key }) => {
                                const src = getPreviewURLApi(id, key, 'good')
                                return (
                                  <div class={'group relative w-full h-296px flex'}>
                                    <ElImage
                                      fit={'contain'}
                                      lazy
                                      src={src}
                                      previewSrcList={[src]}
                                    />
                                  </div>
                                )
                              })}
                            </ElSpace>
                          </div>
                        </div>
                      </div>
                    </ElCol>
                    <ElCol span={12}>
                      <div class={'w-full border-1px border-solid border-transparent'}>
                        <div
                          class={
                            'flex items-center justify-center font-bold  text-white w-full h-40px bg-[var(--el-color-error-light-5)] border-b-1px border-b-solid border-b-[var(--el-border-color)]'
                          }
                        >
                          {t('坏件')}
                        </div>
                        <div class={'py-3px px-3px'}>
                          <div
                            class={
                              'min-h-[296px] py-7px px-7px border-3px border-solid border-[var(--el-color-error-light-5)]'
                            }
                          >
                            <ElSpace wrap class={'w-full'} style={{ justifyContent: 'center' }}>
                              {_badPartImages.map(({ key }) => {
                                const src = getPreviewURLApi(id, key, 'bad')
                                return (
                                  <div class={'group relative w-full h-296px flex'}>
                                    <ElImage
                                      fit={'contain'}
                                      lazy
                                      src={src}
                                      previewSrcList={[src]}
                                    />
                                  </div>
                                )
                              })}
                            </ElSpace>
                          </div>
                        </div>
                      </div>
                    </ElCol>
                  </ElRow>
                )
              }
            }
          }
        }
      ]
    }
  }
)
const handlerFileRemove = async (id: ID, key: string, type: 'good' | 'bad') => {
  const { data } = await deleteImageApi(id, key, type)
  if (!!data) {
    currentRow.value = omitNil(data ?? {})
  }
}
const handlerFileUpload = async (id: ID, file: UploadFile, type: 'good' | 'bad') => {
  const { data } = await uploadApi(id, file, type)
  if (!!data) {
    currentRow.value = omitNil(data ?? {})
    if (type === 'good') {
      goodVisible.value = false
    } else {
      badVisible.value = false
    }
  }
}
const setSortChange = ({
  prop,
  order
}: {
  prop: string
  order: 'ascending' | 'descending' | null
}) => {
  sortParams.value = order === null ? undefined : { prop, order }
  getList()
}

const { getList, refresh } = tableMethods
const exportLoading = ref<boolean>(false)
const writeRef = ref<ComponentRef<typeof Write>>()
const saveLoading = ref(false)
const selectRows = ref<Problem[]>([])
const setSelectionChange = (rows: Problem[]) => {
  selectRows.value = rows
}
const deleteEntity = async () => {
  loading.value = true
  const rows = unref(selectRows)
  try {
    await deleteApi(rows.map((item) => item.id))
    ElMessage.success(t('删除成功'))
    selectRows.value = []
    getList()
  } catch {
  } finally {
    loading.value = false
  }
}
const save = async () => {
  const write = unref(writeRef)
  const formData = await write?.submit()
  if (formData) {
    saveLoading.value = true
    const res = await saveOrUpdateApi(formData)
      .catch(() => {})
      .finally(() => {
        saveLoading.value = false
      })
    if (res) {
      drawerVisible.value = false
      getList()
    }
  }
}
const downloadTemplate = async () => {
  await downloadTplApi()
}

const exportData = async (rows: Problem[]) => {
  exportLoading.value = true
  try {
    const { status, ...rest } = unref(searchParams)
    await exportListApi(
      {
        query: {
          ...rest,
          status:
            Array.isArray(status) && status.length > 0
              ? status
              : [ProblemStatus.NEW, ProblemStatus.CLOSED, ProblemStatus.PROCESSING]
        },
        sort: unref(sortParams)
      },
      rows.map((row) => row.id)
    )
  } finally {
    exportLoading.value = false
  }
}
const exportPdf = async (id: ID) => {
  exportLoading.value = true
  try {
    await exportPdfApi(id)
  } finally {
    exportLoading.value = false
  }
}
const searchForm = ref(null)
useResizeObserver(searchForm, (entries) => {
  const entry = entries[0]
  const { height } = entry.contentRect
  const { height: pHeight = 0 } =
    entry.target.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
const contentRef = ref(null)
useResizeObserver(contentRef, () => {
  const ele = unref(searchForm) as unknown as HTMLDivElement
  const { height } = ele.getBoundingClientRect() ?? {}
  const { height: pHeight = 0 } = ele.parentElement?.parentElement?.getBoundingClientRect() ?? {}
  tableMethods.setProps({ height: pHeight - height - convertRem(124) })
})
const visible = ref(false)
const upload = ref<UploadInstance>()
const files = ref<UploadUserFile[]>([])
const handleExceed: UploadProps['onExceed'] = (files) => {
  upload.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  upload.value!.handleStart(file)
}
const handleChange: UploadProps['onChange'] = (_, uploadFiles) => {
  files.value = uploadFiles
}
const errorFile = ref<{ url: string; name: string }>()
const sendEmail = async () => {
  try {
    await sendEmailApi()
    ElMessage.success('发送完成')
  } catch {}
}
const submitUpload = async () => {
  if (files.value.length !== 1) {
    ElMessage.error('请上传一个文件')
    return
  }
  const [file] = files.value
  if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.XLSX')) {
    ElMessage.error('请上传正确的模板文件')
    return
  }
  saveLoading.value = true
  try {
    const res = await uploadTplApi(file)
    if ((res as any).code === 0) {
      visible.value = false
      ElMessage.success('上传成功')
      refresh()
    } else if (!!(res as any).file) {
      errorFile.value = { ...(res as any).file }
    }
  } catch {
  } finally {
    saveLoading.value = false
  }
}
</script>

<template>
  <ContentWrap ref="contentRef" :bodyStyle="{ height: 'calc(100vh - 14.25rem)' }">
    <div ref="searchForm">
      <Search
        :schema="allSchemas?.searchSchema"
        @search="setSearchParams"
        @reset="setSearchParams"
      />
    </div>

    <div class="mb-10px">
      <BaseButton
        type="primary"
        :disabled="selectRows.length !== 1"
        @click="action(selectRows[0], 'view')"
        v-hasPermi="'problem:view'"
      >
        {{ t('查看') }}
      </BaseButton>
      <BaseButton type="primary" @click="visible = true">
        {{ t('导入') }}
      </BaseButton>
      <BaseButton
        type="primary"
        v-hasPermi="'problem:export'"
        :loading="exportLoading"
        @click="exportData(selectRows)"
      >
        {{ t('导出') }}
      </BaseButton>
      <BaseButton
        type="primary"
        :loading="exportLoading"
        :disabled="selectRows.length !== 1"
        v-hasPermi="'problem:export'"
        @click="exportPdf(selectRows[0].id)"
      >
        {{ t('导出PDF') }}
      </BaseButton>
      <BaseButton
        type="primary"
        v-hasPermi="'problem:edit'"
        :disabled="
          selectRows.length !== 1 ||
          (!!selectRows[0].cqeId && userStore.userInfo?.id !== selectRows[0].cqeId) ||
          (!selectRows[0].cqeId && userStore.userInfo?.id !== selectRows[0].cqeId) ||
          selectRows[0].status === ProblemStatus.CLOSED
        "
        @click="action(selectRows[0], 'edit')"
      >
        {{ t('编辑') }}
      </BaseButton>
      <ElPopconfirm
        v-hasPermi="'problem:delete'"
        :disabled="
          !selectRows.length ||
          selectRows.some(
            (row) =>
              !userStore.isAdmin &&
              row.creatorId !== userStore.userInfo?.id &&
              row.cqeId !== userStore.userInfo?.id
          )
        "
        :title="t('是否确认删除?')"
        @confirm="deleteEntity"
      >
        <template #reference>
          <BaseButton
            :loading="loading"
            :disabled="
              !selectRows.length ||
              selectRows.some(
                (row) =>
                  !userStore.isAdmin &&
                  row.creatorId !== userStore.userInfo?.id &&
                  row.cqeId !== userStore.userInfo?.id
              )
            "
            type="danger"
          >
            {{ t('删除') }}
          </BaseButton>
        </template>
      </ElPopconfirm>
      <ElPopconfirm v-if="userStore.isAdmin" :title="t('是否发送提醒邮件?')" @confirm="sendEmail">
        <template #reference>
          <BaseButton :loading="loading" :disabled="!userStore.isAdmin" type="primary">
            {{ t('发送提醒邮件') }}
          </BaseButton>
        </template>
      </ElPopconfirm>
    </div>

    <Table
      align="center"
      headerAlign="center"
      v-model:pageSize="pageSize"
      v-model:currentPage="currentPage"
      @sort-change="setSortChange"
      @selection-change="setSelectionChange"
      :pagination="{ total }"
      :columns="allSchemas?.tableColumns"
      :data="dataList"
      :loading="loading"
      @register="tableRegister"
      @refresh="refresh"
    />
    <Dialog
      v-model="visible"
      :fullscreen="false"
      :title="`${t('导入')}${t('问题历史数据')}`"
      :maxHeight="80"
    >
      <ElUpload
        ref="upload"
        accept=".xlsx"
        class="upload-demo"
        v-model="files"
        :limit="1"
        :on-change="handleChange"
        :on-exceed="handleExceed"
        :auto-upload="false"
      >
        <template #trigger>
          <el-button type="primary">{{ t('选择文件') }}</el-button>
        </template>
      </ElUpload>
      <template #footer>
        <BaseButton
          v-if="!!errorFile?.name.length"
          type="default"
          @click="
            () => {
              if (!!errorFile) {
                saveAs(errorFile.url, '错误文件.xlsx')
              }
            }
          "
        >
          {{ t('下载错误文件') }}
        </BaseButton>
        <BaseButton type="primary" :loading="saveLoading" @click="submitUpload">
          {{ t('上传') }}
        </BaseButton>
        <BaseButton type="default" @click="downloadTemplate">
          {{ t('下载模板') }}
        </BaseButton>
      </template>
    </Dialog>
    <Drawer
      v-model="drawerVisible"
      :fullscreen="false"
      :title="t(`${!currentRow?.id ? '新增' : actionType === 'view' ? '查看' : '编辑'}问题`)"
      default-fullscreen
    >
      <Write
        ref="writeRef"
        :action-type="actionType"
        :draft="false"
        :form-schema="formSchemas"
        :current-row="currentRow"
      />

      <template #footer>
        <BaseButton
          v-if="actionType !== 'view'"
          type="primary"
          :loading="saveLoading"
          @click="save"
        >
          {{ t('提交') }}
        </BaseButton>
        <BaseButton @click="drawerVisible = false">{{ t('关闭') }}</BaseButton>
      </template>
    </Drawer>
  </ContentWrap>
</template>
