{"name": "hlt-ui", "version": "1.0.0", "description": "问题系统UI。", "author": "上海帆之扬信息技术有限公司 <<EMAIL>>", "private": false, "scripts": {"dev": "pnpm vite --mode dev", "defx": "pnpm vite --mode defx", "ts:check": "pnpm vue-tsc --noEmit --skipLib<PERSON><PERSON>ck", "build:pro": "pnpm vite build --mode pro", "build:dfx": "pnpm vite build --mode dfx", "build:gitee": "pnpm vite build --mode gitee", "build:dev": "pnpm vite build --mode dev", "build:test": "pnpm run ts:check && vite build --mode test", "serve:pro": "pnpm vite preview --mode pro", "serve:dev": "pnpm vite preview --mode dev", "serve:test": "pnpm vite preview --mode test", "npm:check": "pnpx npm-check-updates -u", "clean": "pnpx rimraf node_modules", "clean:cache": "pnpx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "plop": "plop", "icon": "esno ./scripts/icon.ts"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@element-plus/icons-vue": "^2.3.1", "@iconify/iconify": "^3.1.1", "@iconify/vue": "^4.1.1", "@vueuse/core": "^10.7.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.6.2", "cropperjs": "^1.6.1", "dayjs": "^1.11.10", "driver.js": "^1.3.1", "echarts": "^5.4.3", "echarts-wordcloud": "^2.1.0", "element-plus": "2.4.3", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "fingerprintjs2": "^2.1.4", "jspdf": "^2.5.1", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "url": "^0.11.3", "vue": "3.3.10", "vue-i18n": "9.8.0", "vue-json-pretty": "^2.2.4", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "xgplayer": "^3.0.10"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@faker-js/faker": "^8.3.1", "@iconify/json": "^2.2.153", "@intlify/unplugin-vue-i18n": "^1.5.0", "@purge-icons/generated": "^0.10.0", "@types/file-saver": "^2.0.7", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.3", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.10", "@types/sortablejs": "^1.15.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@unocss/transformer-variant-group": "^0.58.0", "@vitejs/plugin-legacy": "^5.2.0", "@vitejs/plugin-vue": "^4.5.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.16", "chalk": "^5.3.0", "consola": "^3.2.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.0.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.19.2", "esno": "^4.0.0", "fs-extra": "^11.2.0", "husky": "^8.0.3", "inquirer": "^9.2.12", "less": "^4.2.0", "lint-staged": "^15.2.0", "plop": "^4.0.0", "postcss": "^8.4.32", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "postcss-pxtorem": "^6.1.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "rollup": "^4.6.1", "stylelint": "^15.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^13.0.0", "stylelint-config-standard": "^34.0.0", "stylelint-order": "^6.0.3", "terser": "^5.25.0", "typescript": "5.3.3", "unocss": "^0.58.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "5.0.6", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "^2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^10.2.0", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.1.0"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kailong321200875/vue-element-plus-admin.git"}, "bugs": {"url": "https://github.com/kailong321200875/vue-element-plus-admin/issues"}, "homepage": "https://github.com/kailong321200875/vue-element-plus-admin"}