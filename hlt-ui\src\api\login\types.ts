export interface UserLoginType {
  username: string
  password: string
}

export interface UserType {
  id?: string
  name?: string
  avatar?: string
  username: string
  password: string
  role: string
  roleId: string[]
  lang: string
}

export interface SysRouter {
  id: number
  parentId?: number
  path: string
  name: string
  title: string
  meta: Record<string, any>
}

export interface Lang {
  id: number
  code: 'zh' | 'en'
  name: string
  message?: Record<string, any>
  langData: { id: number; label: string; value: string }[]
}
