import { SUCCESS_CODE } from '@/constants'
import { MockMethod } from 'vite-plugin-mock'

const timeout = 100

const BASE_URL = '/mock/dashboard'
function get(url: string, response: any) {
  return {
    url: `${BASE_URL}${url}`,
    timeout,
    method: 'get',
    response: typeof response === 'function' ? response : () => response
  }
}
export default [
  // 首页顶部面板数量
  get('/panelCount', {
    code: SUCCESS_CODE,
    data: {
      open: Math.ceil(Math.random() * 200),
      follow: Math.ceil(Math.random() * 200),
      processing: Math.ceil(Math.random() * 200)
    }
  })
] as MockMethod[]
