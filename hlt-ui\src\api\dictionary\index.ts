import request from '@/axios'
import { AxiosHeaders, AxiosResponse } from 'axios'
import { UploadUserFile } from 'element-plus'
import saveAs from 'file-saver'
import type { ID, Result } from '../base/types'
import { PageRequest } from './../../utils/index'
import { Dictionary, DictionaryCategory, DictionaryOption, DictionaryRelation } from './types'

const BASE_API = '/dictionary'

export const getOptionApi = async (id: ID) => {
  return request.get<DictionaryOption[]>({ url: `${BASE_API}/options/${id}` })
}

export const getListApi = async (query: Record<string, any> = {}) => {
  return request.post<Dictionary[]>({ url: `${BASE_API}/list`, data: new PageRequest({ query }) })
}

export const saveOrUpdateApi = async (entity: Dictionary) => {
  return request.post<Result>({ url: `${BASE_API}/`, data: entity })
}

export const getOptionByCategoryApi = async (category: DictionaryCategory) => {
  return request.get<DictionaryOption[]>({ url: `${BASE_API}/${category}/options` })
}

export const getOptionsByCategoriesApi = async (categories: DictionaryCategory[]) => {
  return request.post<{ [k: string]: DictionaryOption[] }>({
    url: `${BASE_API}/options`,
    data: { categories }
  })
}

// 获取工厂字典
export const getFactoriesApi = async () => {
  return getOptionByCategoryApi(DictionaryCategory.FACTORY)
}

// 获取事业部字典
export const getBusinessUnitApi = async () => {
  return getOptionByCategoryApi(DictionaryCategory.BUSINESS_UNIT)
}

// 获取生产线字典
export const getProductLineApi = async () => {
  return getOptionByCategoryApi(DictionaryCategory.PRODUCT_LINE)
}

// 获取客户字典
export const getCustomerApi = async () => {
  return getOptionByCategoryApi(DictionaryCategory.CUSTOMER)
}

// 获取产品阶段字典
export const getProductStepApi = async () => {
  return getOptionByCategoryApi(DictionaryCategory.PRODUCT_STEP)
}
// 获取机型字典
export const getMachineTypeApi = async () => {
  return getOptionByCategoryApi(DictionaryCategory.MACHINE_TYPE)
}

export const downloadRelationApi = async () => {
  const { data, headers } = (await request.get({
    url: `${BASE_API}/relations`,
    responseType: 'blob'
  })) as unknown as AxiosResponse
  let fileName
  if (!!headers) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(headers['content-disposition'])
    fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
  }
  saveAs(data, fileName)
}

export const uploadRelationApi = async (file: UploadUserFile) => {
  const formData = new FormData()
  formData.append('file', file.raw!, file.name)
  const { data, headers } = (await request.post({
    url: `${BASE_API}/relations`,
    data: formData,
    headers: AxiosHeaders.from().setContentType('multipart/form-data'),
    responseType: 'blob'
  })) as unknown as AxiosResponse
  if (data.type === 'application/json') {
    return new Promise((resolve) => {
      const fr = new FileReader()
      fr.onload = function () {
        const { code, message } = JSON.parse(fr.result as string)
        if (code === 1) {
          ElMessage.error(message)
        }
        resolve({ code })
      }
      fr.readAsText(data)
    })
  } else {
    ElMessage.error('导入失败,请下载文件查看错误详情')
    let fileName
    if (!!headers) {
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
      const matches = filenameRegex.exec(headers['content-disposition'])
      fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
    }
    console.log(fileName)
    return {
      code: 1,
      file: {
        url: URL.createObjectURL(data),
        name: fileName
      }
    }
  }
}

export const downloadTplApi = async (category: DictionaryCategory) => {
  const { data, headers } = (await request.get({
    url: `${BASE_API}/${category}/template`,
    responseType: 'blob'
  })) as unknown as AxiosResponse
  let fileName
  if (!!headers) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/
    const matches = filenameRegex.exec(headers['content-disposition'])
    fileName = !!matches && matches.length > 1 ? decodeURI(matches[1]) : ''
  }
  saveAs(data, fileName)
}

export const uploadApi = async (category: DictionaryCategory, file: UploadUserFile) => {
  const formData = new FormData()
  formData.append('file', file.raw!, file.name)
  return request.post<Result>({
    url: `${BASE_API}/${category}/import`,
    data: formData,
    headers: AxiosHeaders.from().setContentType('multipart/form-data')
  })
}

export const getRelationsApi = async () => {
  return request.get<DictionaryRelation[]>({
    url: `${BASE_API}/relations/list`
  })
}
