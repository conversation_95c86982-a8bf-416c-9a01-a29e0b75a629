<script setup lang="ts">
import { useI18n } from '@/hooks/web/useI18n'
import { propTypes } from '@/utils/propTypes'
import { ref, unref, watch } from 'vue'
const { t } = useI18n()
const props = defineProps({
  modelValue: propTypes.string.def(''),
  i18n: propTypes.bool.def(false)
})

watch(
  () => props.modelValue,
  (val: string) => {
    if (val === unref(valueRef)) return
    valueRef.value = val
  }
)

const valueRef = ref(props.modelValue)
</script>

<template>
  <div>
    <span> {{ i18n ? t(valueRef) : valueRef }} </span>
  </div>
</template>
