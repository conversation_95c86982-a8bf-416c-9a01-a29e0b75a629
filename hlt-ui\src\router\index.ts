import { NO_RESET_WHITE_LIST } from '@/constants'
import { useI18n } from '@/hooks/web/useI18n'
import { Layout } from '@/utils/routerHelper'
import type { App } from 'vue'
import type { RouteRecordRaw } from 'vue-router'
import { createRouter, createWebHistory } from 'vue-router'

const { t } = useI18n()

export const constantRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    name: 'Root',
    meta: {
      hidden: true
    }
  },
  {
    path: '/redirect',
    component: Layout,
    name: 'Redirect_Layout',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/404',
    component: () => import('@/views/Error/404.vue'),
    name: 'NoFind',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/dashboard',
    component: Layout,
    name: 'Dashboard',
    meta: {},
    children: [
      {
        path: '',
        component: () => import('@/views/Dashboard/Index.vue'),
        name: 'DashboardIndex',
        meta: {
          title: t('分析面板'),
          icon: 'ant-design:home-filled',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/pdf/:id',
    name: 'PDF',
    meta: { hidden: true },
    component: () => import('@/views/Problem/Info.vue')
  }
]

export const asyncRouterMap: AppRouteRecordRaw[] = [
  {
    path: '/problem',
    component: Layout,
    name: 'Problem',
    meta: {},
    children: [
      {
        path: '',
        component: () => import('@/views/Problem/Index.vue'),
        name: 'ProblemIndex',
        meta: {
          title: '问题列表',
          icon: 'ic:outline-checklist',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/task',
    component: Layout,
    name: 'Task',
    meta: {},
    children: [
      {
        path: '',
        component: () => import('@/views/Task/Index.vue'),
        name: 'TaskIndex',
        meta: {
          title: '我的任务',
          icon: 'jam:task-list',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/audit',
    component: Layout,
    name: 'Audit',
    meta: {},
    children: [
      {
        path: '',
        component: () => import('@/views/Audit/Index.vue'),
        name: 'AuditIndex',
        meta: {
          title: '我的审批',
          icon: 'ant-design:audit-outlined',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/stats',
    component: Layout,
    name: 'Stats',
    meta: {},
    children: [
      {
        path: '',
        component: () => import('@/views/Stats/Index.vue'),
        name: 'StatIndex',
        meta: {
          title: '统计报表',
          icon: 'ic:baseline-bar-chart',
          noCache: false
        }
      }
    ]
  },
  {
    path: '/basic',
    component: Layout,
    name: 'Basic',
    meta: {
      title: t('基础数据'),
      icon: 'ant-design:setting-filled',
      alwaysShow: true
    },
    children: [
      {
        path: 'dictionary',
        component: () => import('@/views/Dictionary/Dictionary.vue'),
        name: 'Dictionary',
        meta: {
          title: t('字典管理')
        }
      },
      {
        path: 'code-rule',
        component: () => import('@/views/CodeRule/CodeRule.vue'),
        name: 'CodeRule',
        meta: {
          title: t('编码规则')
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  strict: true,
  routes: constantRouterMap as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !NO_RESET_WHITE_LIST.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  app.use(router)
}

export default router
