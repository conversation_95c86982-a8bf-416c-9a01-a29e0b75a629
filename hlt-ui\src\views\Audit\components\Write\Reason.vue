<script setup lang="tsx">
import { getReasonList<PERSON>pi, submitApi } from '@/api/audit'
import { ID } from '@/api/base/types'
import { NodeState, Reason, ReasonDetail, ReasonDetailType } from '@/api/problem/types'
import { getDepartmentsApi, getUsersApi } from '@/api/user'
import { Department, User } from '@/api/user/types'
import { BaseButton } from '@/components/Button'
import { DescriptionsSchema } from '@/components/Descriptions'
import { ComponentNameEnum, FormSchema } from '@/components/Form'
import { ReasonDetailItem } from '@/components/Form/src/components/ReasonDetail'
import { Table, TableColumn } from '@/components/Table'
import { useDesign } from '@/hooks/web/useDesign'
import { useI18n } from '@/hooks/web/useI18n'
import { useTable } from '@/hooks/web/useTable'
import { useUserStore } from '@/store/modules/user'
import { propTypes } from '@/utils/propTypes'
import { ElMessage, ElMessageBox, ElTag } from 'element-plus'
import ReasonForm from './ReasonForm.vue'

const { t } = useI18n()
const { getPrefixCls } = useDesign()

const dialogVisible = ref(false)
const prefixCls = getPrefixCls('log')
const userStore = useUserStore()
const props = defineProps({
  readonly: propTypes.bool.def(false),
  id: String as PropType<ID>
})

const currentRow = ref<Reason>()
const genFormData = (row: Reason) => {
  const {
    id,
    configs,
    details,
    category,
    subCategory,
    improvement,
    unqualityType,
    unqualityCode,
    estimatedFinishOn,
    validateResult,
    state,
    stateIdx
  } = row
  const config = configs.find((item) => item.state === NodeState.ANALYZE)
  const detailRecord: Record<ReasonDetailType, ReasonDetail[]> = {
    [ReasonDetailType.EXPOSE]: [],
    [ReasonDetailType.PRODUCE]: [],
    [ReasonDetailType.SYSTEM]: []
  }
  for (const detail of details) {
    detailRecord[detail.type].push(detail)
  }
  currentRow.value = {
    id,
    category,
    subCategory,
    improvement: improvement ?? undefined,
    unqualityType: unqualityType ?? undefined,
    unqualityCode: unqualityCode ?? undefined,
    estimatedFinishOn,
    details: detailRecord,
    ownerName: config?.ownerName,
    ownerDepartment: config?.ownerDepartment,
    validateResult: validateResult ?? undefined,
    state,
    stateIdx
  } as any
}
const actionType = ref('')
const usersRef = ref<User[]>([])
const departmentsRef = ref<Department[]>([])
const action = async (row: Reason, type: string) => {
  actionType.value = type
  genFormData(row)
  dialogVisible.value = true
}
watch(
  () => dialogVisible.value,
  (visible) => {
    if (!visible) {
      actionType.value = ''
      currentRow.value = undefined
    }
  },
  { immediate: true }
)
const { tableRegister, tableMethods, tableState } = useTable({
  fetchDataApi: async () => {
    const res = await getReasonListApi(props.id!)
    const { data } = res
    const _data = data.filter(
      (item) =>
        (item.state === NodeState.ANALYZE_AUDIT ||
          item.state === NodeState.VALIDATE_AUDIT ||
          item.state === NodeState.CQE_AUDIT) &&
        item.configs.find(
          (config) => config.stateIdx === item.stateIdx && config.ownerId === userStore.userInfo?.id
        )
    )
    if (!!currentRow.value) {
      const row = _data.find((item) => item.id === currentRow.value?.id)
      if (!!row) {
        genFormData(row)
      }
    }
    return {
      list: _data,
      total: _data.length
    }
  }
})
const { loading, dataList } = tableState
const { refresh } = tableMethods
const saveLoading = ref(false)
const tableColumns: TableColumn[] = [
  {
    field: 'index',
    label: t('序号'),
    type: 'index'
  },
  {
    field: 'category',
    label: t('原因类别(一级)'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'subCategory',
    label: t('原因类别(二级)'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'unqualityType',
    label: t('不良类别'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'unqualityCode',
    label: t('不良代码'),
    width: 120,
    formatter: (_, __, val) => t(val)
  },
  {
    field: 'improvement',
    label: t('行动计划'),
    width: 120
  },
  {
    field: 'status',
    label: t('状态'),
    width: 120
  },
  {
    field: 'estimatedFinishOn',
    label: t('预计完成日期'),
    width: 120
  },
  {
    field: 'finishOn',
    label: t('实际完成日期'),
    width: 120
  },
  {
    field: 'responsible',
    label: t('责任人'),
    formatter: (entity: Recordable, __: TableColumn) => {
      return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerName
    }
  },
  {
    field: 'responsibleDep',
    label: t('部门'),
    formatter: (entity: Recordable, __: TableColumn) => {
      return entity.configs.find((config) => config.state === NodeState.ANALYZE).ownerDepartment
    }
  },
  {
    field: 'validateResult',
    label: t('验证结果')
  },
  {
    field: 'validateOn',
    label: t('验证日期')
  },
  {
    field: 'action',
    label: t('操作'),
    width: 200,
    fixed: 'left',
    slots: {
      default: (data: { row: Reason }) => {
        if (data.row.delete) {
          return <ElTag type="info">{t('已作废')}</ElTag>
        }
        const statusIdxes = data.row.configs
          .filter((config) => config.ownerId === userStore.userInfo?.id)
          .map((config) => config.stateIdx ?? 0)
        const canProcess = statusIdxes.includes(data.row.stateIdx)
        return (
          <>
            <BaseButton type="text" onClick={() => action(data.row, 'view')}>
              {t('查看')}
            </BaseButton>
            <BaseButton
              disabled={!canProcess}
              type="text"
              onClick={() => action(data.row, 'audit')}
            >
              {t('审批')}
            </BaseButton>
          </>
        )
      }
    }
  }
]
const formSchemas = ref<FormSchema[]>([
  {
    field: 'category',
    label: t('原因类别(一级)'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { span: 6 }
  },
  {
    field: 'subCategory',
    label: t('原因类别(二级)'),
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true },
    colProps: { span: 6 }
  },
  {
    field: 'ownerName',
    label: t('原因负责人'),
    component: ComponentNameEnum.TEXT,
    colProps: { span: 6 }
  },
  {
    field: 'ownerDepartment',
    label: t('部门'),
    component: ComponentNameEnum.TEXT,
    colProps: { span: 6 }
  },
  {
    field: 'details',
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return (
            <ReasonDetailItem
              v-model={formValue.details}
              entityId={formValue.id}
              readonly={props.readonly}
              onChange={() => {
                refresh()
              }}
            />
          )
        }
      }
    }
  },
  {
    field: 'improvement',
    label: t('行动计划'),
    colProps: { span: 24 },
    formItemProps: {
      slots: props.readonly
        ? {
            default: ({ formValue }: any) => {
              return (
                <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                  {formValue.improvement}
                </p>
              )
            }
          }
        : undefined
    }
  },
  {
    field: 'unqualityType',
    label: t('不良类别'),
    colProps: { span: 6 },
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true }
  },
  {
    field: 'unqualityCode',
    label: t('不良代码'),
    colProps: { span: 6 },
    component: ComponentNameEnum.TEXT,
    componentProps: { i18n: true }
  },
  {
    field: 'estimatedFinishOn',
    label: t('预计完成日期'),
    colProps: { span: 6 },
    component: ComponentNameEnum.TEXT
  }
])

onMounted(async () => {
  const userRes = await getUsersApi()
  usersRef.value = userRes.data
  const departmentsRes = await getDepartmentsApi()
  departmentsRef.value = departmentsRes.data
})
watch(
  () => currentRow.value,
  (row) => {
    const _schemas: FormSchema[] = [
      {
        field: 'category',
        label: t('原因类别(一级)'),
        component: ComponentNameEnum.TEXT,
        componentProps: { i18n: true },
        colProps: { span: 6 }
      },
      {
        field: 'subCategory',
        label: t('原因类别(二级)'),
        component: ComponentNameEnum.TEXT,
        componentProps: { i18n: true },
        colProps: { span: 6 }
      },
      {
        field: 'ownerName',
        label: t('原因负责人'),
        component: ComponentNameEnum.TEXT,
        colProps: { span: 6 }
      },
      {
        field: 'ownerDepartment',
        label: t('部门'),
        component: ComponentNameEnum.TEXT,
        colProps: { span: 6 }
      },
      {
        field: 'details',
        colProps: { span: 24 },
        formItemProps: {
          slots: {
            default: ({ formValue }: any) => {
              return (
                <ReasonDetailItem
                  v-model={formValue.details}
                  entityId={formValue.id}
                  readonly={props.readonly}
                  onChange={() => {
                    refresh()
                  }}
                />
              )
            }
          }
        }
      },
      {
        field: 'improvement',
        label: t('行动计划'),
        colProps: { span: 24 },
        formItemProps: {
          slots: props.readonly
            ? {
                default: ({ formValue }: any) => {
                  return (
                    <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                      {formValue.improvement}
                    </p>
                  )
                }
              }
            : undefined
        }
      },
      {
        field: 'unqualityType',
        label: t('不良类别'),
        colProps: { span: 6 },
        component: ComponentNameEnum.TEXT,
        componentProps: { i18n: true }
      },
      {
        field: 'unqualityCode',
        label: t('错不良代码误代码'),
        colProps: { span: 6 },
        component: ComponentNameEnum.TEXT,
        componentProps: { i18n: true }
      },
      {
        field: 'estimatedFinishOn',
        label: t('预计完成日期'),
        colProps: { span: 6 },
        component: ComponentNameEnum.TEXT
      }
    ]
    if ((row?.stateIdx ?? 0) >= 3) {
      _schemas.push({
        field: 'validateResult',
        label: t('效果验证'),
        colProps: { span: 24 },
        formItemProps: {
          slots: {
            default: ({ formValue }: any) => {
              return (
                <p class="w-full m0" style={{ whiteSpace: 'pre-wrap' }}>
                  {formValue.validateResult}
                </p>
              )
            }
          }
        }
      })
    }
    formSchemas.value = _schemas
  },
  { immediate: true }
)
const formRef = ref<ComponentRef<typeof ReasonForm>>()
const schemas: DescriptionsSchema[] = reactive([
  {
    field: 'reasons',
    span: 24,
    slots: {
      default: (data) => {
        return (
          <Table
            align="center"
            headerAlign="center"
            columns={tableColumns}
            data={data}
            loading={loading.value}
            onRegister={tableRegister}
            onRefresh={refresh}
          />
        )
      }
    }
  }
])
const approve = () => {
  ElMessageBox.confirm(t('是否确认通过审批'), t('确认操作'), {
    confirmButtonText: t('确认'),
    cancelButtonText: t('取消'),
    type: 'warning'
  }).then(async () => {
    saveLoading.value = true
    try {
      const res = await submitApi(currentRow.value!.id!, { approved: true })
      if (res) {
        ElMessage.success(t('审批通过成功'))
        refresh()
        dialogVisible.value = false
      }
    } catch {
    } finally {
      saveLoading.value = false
    }
  })
}
const reject = () => {
  ElMessageBox.prompt(t('是否确认驳回审批'), t('确认操作'), {
    confirmButtonText: t('确认'),
    cancelButtonText: t('取消'),
    type: 'warning',
    inputValidator: (value: string) => {
      return !!(value ?? '').trim().length
    },
    inputErrorMessage: t('请填写审批驳回理由')
  }).then(async ({ value }) => {
    saveLoading.value = true
    try {
      const res = await submitApi(currentRow.value!.id!, { approved: false, remark: value })
      if (res) {
        ElMessage.success(t('审批驳回成功'))
        refresh()
        dialogVisible.value = false
      }
    } catch {
    } finally {
      saveLoading.value = false
    }
  })
}
</script>

<template>
  <Descriptions :class="prefixCls" :title="t('原因分析')" :data="dataList" :schema="schemas" />
  <Drawer
    v-model="dialogVisible"
    :title="t('原因分析及行动计划')"
    :fullscreen="false"
    default-fullscreen
  >
    <ReasonForm
      ref="formRef"
      :action-type="actionType"
      :schemas="formSchemas"
      :current-row="currentRow"
    />

    <template #footer>
      <BaseButton v-if="actionType === 'view'" type="default" @click="dialogVisible = false">
        {{ t('关闭') }}
      </BaseButton>
      <BaseButton
        v-if="actionType === 'audit'"
        type="primary"
        :loading="saveLoading"
        @click="approve()"
      >
        {{ t('批准') }}
      </BaseButton>
      <BaseButton
        v-if="actionType === 'audit'"
        type="danger"
        :loading="saveLoading"
        @click="reject()"
      >
        {{ t('驳回') }}
      </BaseButton>
    </template>
  </Drawer>
</template>
<style lang="less" scoped>
@prefix-cls: ~'@{elNamespace}-descriptions';

.@{prefix-cls} {
  :deep(&__label) {
    display: none;
  }

  :deep(&__content) {
    padding: 0 !important;
  }
}
</style>
