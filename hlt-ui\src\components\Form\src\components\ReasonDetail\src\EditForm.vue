<script setup lang="tsx">
import { ID } from '@/api/base/types'
import { ReasonDetailType } from '@/api/problem/types'
import { ComponentNameEnum, Form, FormSchema } from '@/components/Form'
import { useForm } from '@/hooks/web/useForm'
import { useValidator } from '@/hooks/web/useValidator'
import { UploadFile } from 'element-plus'
import { PropType } from 'vue'
import { Uploader } from '../../Uploader'
import { useI18n } from '@/hooks/web/useI18n'
const { required, whitespace } = useValidator()

const { t } = useI18n()
export type ReasonDetailDto = {
  id: ID
  type: ReasonDetailType
  question: string
  answer: string
  evidence: string
  file: UploadFile
}

const props = defineProps({
  currentRow: {
    type: Object as PropType<Nullable<Partial<ReasonDetailDto>>>,
    default: () => null
  }
})
const rules = reactive({
  question: [required(), whitespace()],
  answer: [required(), whitespace()],
  evidence: [required(), whitespace()]
})

const { formRegister, formMethods } = useForm()
const { setValues, getFormData, getElFormExpose } = formMethods

const submit = async (skipValidate: boolean = false) => {
  const elForm = await getElFormExpose()
  const valid =
    skipValidate ||
    (await elForm?.validate().catch((err) => {
      console.log(err)
    }))
  if (valid) {
    return await getFormData<ReasonDetailDto>()
  }
}

watch(
  () => props.currentRow,
  (currentRow) => {
    if (!currentRow) return
    setValues(currentRow)
  },
  {
    deep: true,
    immediate: true
  }
)
defineExpose({
  submit
})
const schemas = reactive<FormSchema[]>([
  {
    field: 'question',
    label: t('问题'),
    colProps: { span: 24 },
    component: ComponentNameEnum.INPUT,
    componentProps: { showWordLimit: true, maxlength: 500 }
  },
  {
    field: 'answer',
    label: t('答案'),
    colProps: { span: 24 },
    component: ComponentNameEnum.INPUT,
    componentProps: { showWordLimit: true, maxlength: 500 }
  },
  {
    field: 'evidence',
    label: t('证据'),
    colProps: { span: 24 },
    component: ComponentNameEnum.INPUT,
    componentProps: {
      showWordLimit: true,
      maxlength: 1000,
      autosize: { minRows: 6, maxRows: 10 },
      type: 'textarea'
    }
  },
  {
    field: 'file',
    label: t('附件'),
    colProps: { span: 24 },
    formItemProps: {
      slots: {
        default: ({ formValue }: any) => {
          return <Uploader v-model={formValue.file} />
        }
      }
    }
  }
])
</script>

<template>
  <Form labelPosition="top" :rules="rules" :schema="schemas" @register="formRegister" />
</template>
