@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

.el-table {
  --el-table-header-bg-color: #f5f7fa !important;

  /* thead th.el-table__cell {
    background: var(--el-fill-color-light);
  }
 */
  .el-table__body-wrapper .el-table-column--selection>.cell {
    justify-content: center;
  }

  .el-table__cell>.cell {
    word-break: break-word;
  }
}

.el-form-item .el-form-item {
  margin-bottom: 18px !important;
}

.el-menu {
  --el-menu-level-padding: 10px !important;
}

.el-form-item .el-form-item__label {
  white-space: pre-wrap;
}

.el-tree-node .el-form-item {
  margin-bottom: 0 !important;
}

.el-table__body-wrapper .el-scrollbar {
  .el-scrollbar__bar.is-horizontal {
    height: 10px;
    text-align: left;

    &>div {
      height: 100%;
    }
  }
}

.el-menu-item .el-badge .el-badge__content {
  --el-badge-size: 14px;

  border: 0;
  right: 5px;
}

.el-dialog {
  max-height: 100vh;
}

.el-upload--picture-card {
  --el-upload-picture-card-size: 296px !important;
}

.el-form-item .el-form-item__label {
  white-space: nowrap !important;
}

.el-select .el-input__inner {
  height: var(--el-input-inner-height) !important;
}