<script lang="tsx">
import { BaseButton } from '@/components/Button'
import { FormProps, FormSchema } from '@/components/Form'
import { Icon } from '@/components/Icon'
import { propTypes } from '@/utils/propTypes'
import { ElCol, ElFormItem, ElRow } from 'element-plus'
import { PropType, computed, defineComponent, ref, unref, watch } from 'vue'
import { ListEventSymbol } from '../../../helper'
import { useI18n } from '@/hooks/web/useI18n'
type RecordOrRecordFunc = (() => Record<string, any>) | Record<string, any>

export default defineComponent({
  name: 'FormList',
  props: {
    readonly: propTypes.bool.def(false),
    field: propTypes.string,
    alwayShowLabel: propTypes.bool.def(true),
    shape: propTypes
      .custom<RecordOrRecordFunc>((value) => {
        return typeof value === 'function' || typeof value === 'object'
      })
      .def({}),
    schema: {
      type: Array as PropType<FormSchema[]>,
      default: () => []
    },
    modelValue: {
      type: Array as PropType<any[]>,
      default: () => []
    },
    renderFormItem: propTypes.func
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const { t } = useI18n()
    const mergeProps = ref<FormProps>({})
    const getProps = computed(() => {
      const propsObj = { ...props }
      Object.assign(propsObj, unref(mergeProps))
      return propsObj
    })

    const formModel = ref<Recordable[]>(props.modelValue)
    watch(
      () => props.modelValue,
      (val: Recordable[]) => {
        if (val === unref(formModel)) return
        formModel.value = val
      }
    )
    const setValues = (data: Recordable) => {
      emit('update:modelValue', data)
    }

    const renderFormItem = unref(getProps).renderFormItem
    const shape = computed(() => props.shape)
    const formSchema = computed(() => props.schema)

    const addItem = () => {
      const newItem = typeof shape.value === 'function' ? shape.value() : shape.value
      const rawItems = unref(formModel)
      formModel.value = [...rawItems, newItem]
      setValues({ [ListEventSymbol]: 'add', data: newItem })
    }
    const removeItem = (idx: number) => {
      formModel.value = formModel.value.filter((_, i) => i !== idx)
      setValues({ [ListEventSymbol]: 'delete', idx })
    }
    const contentStyle = computed(() => ({ height: '100%' }))
    return () => (
      <ElRow>
        {formModel.value.map((_item, index) => {
          const { alwayShowLabel } = unref(getProps)
          return (
            <ElRow class={'mb-15px w-full'}>
              <ElCol span={22}>
                <ElRow gutter={20}>
                  {renderFormItem?.(unref(formSchema), index, alwayShowLabel)}
                </ElRow>
              </ElCol>
              <ElCol span={2}>
                <ElFormItem
                  v-contentStyle={contentStyle}
                  v-slots={
                    alwayShowLabel || index === 0
                      ? {
                          label: () => (
                            <span
                              style={{
                                opacity: 0,
                                color: 'transparent',
                                userSelect: 'none'
                              }}
                            >
                              O
                            </span>
                          )
                        }
                      : undefined
                  }
                >
                  {!props.readonly && (
                    <BaseButton
                      class="mt-4px"
                      onClick={() => {
                        removeItem(index)
                      }}
                      type="danger"
                      link
                    >
                      <Icon icon="ant-design:delete-outlined" />
                    </BaseButton>
                  )}
                </ElFormItem>
              </ElCol>
            </ElRow>
          )
        })}
        {!props.readonly && (
          <ElCol span={24}>
            <BaseButton
              type="default"
              onClick={() => {
                addItem()
              }}
              style={{ width: '100%' }}
            >
              <Icon icon="ant-design:plus-outlined" class="mr-4px" />
              {t('增加一项')}
            </BaseButton>
          </ElCol>
        )}
      </ElRow>
    )
  }
})
</script>
