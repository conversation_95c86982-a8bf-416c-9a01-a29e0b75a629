<script lang="ts" setup>
import { getReasonChartApi } from '@/api/dashboard'
import { SearchParams } from '@/api/dashboard/types'
import { DictionaryCategory, DictionaryOption } from '@/api/dictionary/types'
import { useDashboardStore } from '@/store/modules/dashboard'
import { Radar } from '@antv/g2plot'
import { getListApi } from '@/api/dictionary'
import dayjs from 'dayjs'
import { useI18n } from '@/hooks/web/useI18n'
const props = defineProps<{
  start: Date
  end: Date
  loading: { status: boolean }
}>()

const store = useDashboardStore()
const { t } = useI18n()
const $emit = defineEmits(['update:loading'])

$emit('update:loading', { status: true })

const containerRef = ref<HTMLDivElement>()
const options = ref<DictionaryOption[]>([])
const parent = ref<string>()
const parentChartData = ref<{ name: string; value: number }[]>()
const radarPlot = ref<Radar>()
watchEffect(async () => {
  if (!!radarPlot.value) {
    const { data: valueData } = await getReasonChartApi({
      ...store.filter,
      monthRange: store.datepicker ? [props.start, props.end] : (store.filter as any)?.monthRange
    } as SearchParams)
    if (!!parent.value) {
      const { data: valueData } = await getReasonChartApi(
        {
          ...store.filter,
          monthRange: store.datepicker
            ? [props.start, props.end]
            : (store.filter as any)?.monthRange
        } as SearchParams,
        parent.value
      )
      radarPlot.value.changeData(
        (options.value.find((option) => option.name === parent.value)?.children ?? []).map(
          (option) => {
            return {
              name: option.name,
              value: parseInt(
                valueData.data.find((item) => item.type === option.name)?.value ?? '0',
                10
              )
            }
          }
        )
      )
    } else {
      parentChartData.value = options.value.map((option) => {
        return {
          name: option.name,
          value: parseInt(
            valueData.data.find((item) => item.type === option.name)?.value ?? '0',
            10
          )
        }
      })
      radarPlot.value.changeData(parentChartData.value)
    }
  }
})
onMounted(async () => {
  const { data: dictData } = await getListApi({ category: [DictionaryCategory.REASON_TYPE] })
  if (dictData.length > 0) {
    options.value = dictData[0].options
  }
  const { data: valueData } = await getReasonChartApi({
    ...store.filter,
    monthRange: store.datepicker ? [props.start, props.end] : (store.filter as any)?.monthRange
  } as SearchParams)
  parentChartData.value = options.value.map((option) => {
    return {
      name: option.name,
      value: parseInt(valueData.data.find((item) => item.type === option.name)?.value ?? '0', 10)
    }
  })
  if (!!containerRef.value) {
    radarPlot.value = new Radar(containerRef.value, {
      data: parentChartData.value,
      xField: 'name',
      yField: 'value',
      appendPadding: [0, 10, 0, 10],
      color: [
        'rgb(0, 157, 255)',
        'rgb(34, 228, 255)',
        'rgb(59, 255, 208)',
        'rgb(4, 227, 138)',
        'rgb(157, 255, 134)',
        'rgb(254, 229, 136)'
      ],
      meta: {
        name: {
          formatter: (v) => t(v)
        },
        value: {
          alias: t('问题数量'),
          min: 0,
          nice: true,
          formatter: (v) => Number(v)
        }
      },
      xAxis: {
        line: null,
        tickLine: null,
        grid: {
          line: {
            style: {
              lineDash: null
            }
          }
        }
      },
      yAxis: {
        label: false,
        line: null,
        tickLine: null,
        grid: {
          line: {
            type: 'line',
            style: {
              lineDash: null
            }
          }
        }
      },
      point: {
        size: 2
      }
    })
    containerRef.value.oncontextmenu = (e) => {
      e.preventDefault()
    }
    radarPlot.value.on('point:click', async ({ data }) => {
      if (!parent.value) {
        parent.value = data.data.name
        const { data: valueData } = await getReasonChartApi(
          {
            ...store.filter,
            monthRange: store.datepicker
              ? [props.start, props.end]
              : (store.filter as any)?.monthRange
          } as SearchParams,
          data.data.name
        )
        radarPlot.value!.changeData(
          (options.value.find((option) => option.name === data.data.name)?.children ?? []).map(
            (option) => {
              return {
                name: option.name,
                value: parseInt(
                  valueData.data.find((item) => item.type === option.name)?.value ?? '0',
                  10
                )
              }
            }
          )
        )
      } else {
        if (data.data.value === 0) {
          return
        }
        store.seriesFilter = { category: [parent.value], subCategory: [data.data.name] }
        if (store.datepicker) {
          store.monthRange = [
            dayjs(props.start).startOf('M').toDate(),
            dayjs(props.end).endOf('M').toDate()
          ]
        } else {
          store.monthRange = (store.filter as any).monthRange
        }
        store.setTableDialogVisible(true)
      }
    })
    radarPlot.value.on('plot:contextmenu', () => {
      if (!!parent.value?.length) {
        radarPlot.value!.changeData(parentChartData.value)
        parent.value = undefined
      }
    })
    radarPlot.value.render()
  }
})
</script>
<template>
  <div ref="containerRef" class="h-300px"></div>
</template>
