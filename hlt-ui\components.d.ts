/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActionButton: typeof import('./src/components/Search/src/components/ActionButton.vue')['default']
    Backtop: typeof import('./src/components/Backtop/src/Backtop.vue')['default']
    Breadcrumb: typeof import('./src/components/Breadcrumb/src/Breadcrumb.vue')['default']
    Button: typeof import('./src/components/Button/src/Button.vue')['default']
    Collapse: typeof import('./src/components/Collapse/src/Collapse.vue')['default']
    ColorRadioPicker: typeof import('./src/components/Setting/src/components/ColorRadioPicker.vue')['default']
    ConfigGlobal: typeof import('./src/components/ConfigGlobal/src/ConfigGlobal.vue')['default']
    ContentDetailWrap: typeof import('./src/components/ContentDetailWrap/src/ContentDetailWrap.vue')['default']
    ContentWrap: typeof import('./src/components/ContentWrap/src/ContentWrap.vue')['default']
    ContextMenu: typeof import('./src/components/ContextMenu/src/ContextMenu.vue')['default']
    CountTo: typeof import('./src/components/CountTo/src/CountTo.vue')['default']
    CtDate: typeof import('./src/components/Form/src/components/CtDate/src/CtDate.vue')['default']
    Descriptions: typeof import('./src/components/Descriptions/src/Descriptions.vue')['default']
    Dialog: typeof import('./src/components/Dialog/src/Dialog.vue')['default']
    Drawer: typeof import('./src/components/Drawer/src/Drawer.vue')['default']
    Echart: typeof import('./src/components/Echart/src/Echart.vue')['default']
    EditForm: typeof import('./src/components/Form/src/components/ReasonDetail/src/EditForm.vue')['default']
    Editor: typeof import('./src/components/Editor/src/Editor.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Error: typeof import('./src/components/Error/src/Error.vue')['default']
    Footer: typeof import('./src/components/Footer/src/Footer.vue')['default']
    Form: typeof import('./src/components/Form/src/Form.vue')['default']
    FormList: typeof import('./src/components/Form/src/components/FormList/src/FormList.vue')['default']
    Highlight: typeof import('./src/components/Highlight/src/Highlight.vue')['default']
    Icon: typeof import('./src/components/Icon/src/Icon.vue')['default']
    IconPicker: typeof import('./src/components/IconPicker/src/IconPicker.vue')['default']
    ImageCropping: typeof import('./src/components/ImageCropping/src/ImageCropping.vue')['default']
    ImageViewer: typeof import('./src/components/ImageViewer/src/ImageViewer.vue')['default']
    Infotip: typeof import('./src/components/Infotip/src/Infotip.vue')['default']
    InputPassword: typeof import('./src/components/InputPassword/src/InputPassword.vue')['default']
    InterfaceDisplay: typeof import('./src/components/Setting/src/components/InterfaceDisplay.vue')['default']
    JsonEditor: typeof import('./src/components/JsonEditor/src/JsonEditor.vue')['default']
    LayoutRadioPicker: typeof import('./src/components/Setting/src/components/LayoutRadioPicker.vue')['default']
    LocaleDropdown: typeof import('./src/components/LocaleDropdown/src/LocaleDropdown.vue')['default']
    LockDialog: typeof import('./src/components/UserInfo/src/components/LockDialog.vue')['default']
    LockPage: typeof import('./src/components/UserInfo/src/components/LockPage.vue')['default']
    Logo: typeof import('./src/components/Logo/src/Logo.vue')['default']
    Menu: typeof import('./src/components/Menu/src/Menu.vue')['default']
    Permission: typeof import('./src/components/Permission/src/Permission.vue')['default']
    Qrcode: typeof import('./src/components/Qrcode/src/Qrcode.vue')['default']
    ReadonlyText: typeof import('./src/components/Form/src/components/ReadonlyText/src/ReadonlyText.vue')['default']
    ReasonDetail: typeof import('./src/components/Form/src/components/ReasonDetail/src/ReasonDetail.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Screenfull: typeof import('./src/components/Screenfull/src/Screenfull.vue')['default']
    Search: typeof import('./src/components/Search/src/Search.vue')['default']
    Setting: typeof import('./src/components/Setting/src/Setting.vue')['default']
    SizeDropdown: typeof import('./src/components/SizeDropdown/src/SizeDropdown.vue')['default']
    StaffPicker: typeof import('./src/components/Form/src/components/StaffPicker/src/StaffPicker.vue')['default']
    Table: typeof import('./src/components/Table/src/Table.vue')['default']
    TableActions: typeof import('./src/components/Table/src/components/TableActions.vue')['default']
    TabMenu: typeof import('./src/components/TabMenu/src/TabMenu.vue')['default']
    TagsView: typeof import('./src/components/TagsView/src/TagsView.vue')['default']
    ThemeSwitch: typeof import('./src/components/ThemeSwitch/src/ThemeSwitch.vue')['default']
    Tree: typeof import('./src/components/Form/src/components/Tree/src/Tree.vue')['default']
    Uploader: typeof import('./src/components/Form/src/components/Uploader/src/Uploader.vue')['default']
    UserInfo: typeof import('./src/components/UserInfo/src/UserInfo.vue')['default']
    VideoPlayer: typeof import('./src/components/VideoPlayer/src/VideoPlayer.vue')['default']
    VideoPlayerViewer: typeof import('./src/components/VideoPlayerViewer/src/VideoPlayerViewer.vue')['default']
    Waterfall: typeof import('./src/components/Waterfall/src/Waterfall.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
