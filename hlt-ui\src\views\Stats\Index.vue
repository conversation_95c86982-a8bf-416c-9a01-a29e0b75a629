<script setup lang="ts">
import { getListApi } from '@/api/dictionary'
import { DictionaryCategory } from '@/api/dictionary/types'
import { ComponentNameEnum, FormSchema } from '@/components/Form'
import { useI18n } from '@/hooks/web/useI18n'
import { useDashboardStore } from '@/store/modules/dashboard'
import { Operators } from '@/utils'
import TableDialog from '@/views/Dashboard/components/Charts/TableDialog.vue'
import dayjs from 'dayjs'
import { ElCol, ElRow, ElSkeleton } from 'element-plus'
import ProductBadTypeChart from '../Dashboard/components/Charts/ProductBadTypeChart/index.vue'
import ProductCustomerChart from '../Dashboard/components/Charts/ProductCustomerChart/index.vue'
import ProductReasonChart from '../Dashboard/components/Charts/ProductReasonChart/index.vue'
import ProductStepChart from '../Dashboard/components/Charts/ProductStepChart/index.vue'

defineOptions({
  name: 'StatIndex'
})

const { t } = useI18n()
const store = useDashboardStore()
store.datepicker = false
const searchSchema = ref<FormSchema[]>([])

const init = async () => {
  try {
    const res = await getListApi({
      category: [
        DictionaryCategory.CUSTOMER,
        DictionaryCategory.PRODUCT_STEP,
        DictionaryCategory.PRODUCT_LINE,
        DictionaryCategory.BUSINESS_UNIT,
        DictionaryCategory.MACHINE_CATEGORY,
        DictionaryCategory.MACHINE_TYPE,
        DictionaryCategory.FACTORY,
        DictionaryCategory.UNQUALITY_TYPE,
        DictionaryCategory.REASON_TYPE
      ]
    })
    const options: Record<string, string[]> = {}
    for (const datum of res.data) {
      options[datum.category] = datum.options.map((op) => {
        if (!!op.children?.length) {
          store.setOptions(
            op.name,
            op.children.map((c) => c.name)
          )
        }
        return op.name
      })
    }
    store.setOptions(options)
  } catch (e) {}
}

const months = reactive<[Date, Date]>([dayjs().add(-11, 'M').toDate(), new Date()])
const initialModel = { monthRange: months }
store.setFilter({ monthRange: months } as any)
const lastNMonths = (lastN: number) => {
  return [
    dayjs()
      .add(-1 * (lastN - 1), 'M')
      .toDate(),
    new Date()
  ]
}
const shortcuts: { text: string; value: any | Function }[] = [
  {
    text: t('本月'),
    value: [new Date(), new Date()]
  },
  {
    text: t('今年'),
    value: () => {
      const end = new Date()
      const start = new Date(new Date().getFullYear(), 0)
      return [start, end]
    }
  },
  {
    text: t('过去12个月'),
    value: () => lastNMonths(12)
  },
  {
    text: t('过去6个月'),
    value: () => lastNMonths(6)
  },
  {
    text: t('过去3个月'),
    value: () => lastNMonths(3)
  }
]

onMounted(async () => {
  await init()
  searchSchema.value = [
    {
      field: 'monthRange',
      label: t('创建日期'),
      component: ComponentNameEnum.DATE_PICKER,
      componentProps: { type: 'monthrange', shortcuts, unlinkPanels: true, clearable: false }
    },
    {
      field: 'customer',
      label: t('客户'),
      component: ComponentNameEnum.SELECT,
      formItemProps: {},
      componentProps: {
        multiple: true,
        collapseTags: true,
        filterable: true,
        options: store
          .getOptions(DictionaryCategory.CUSTOMER)
          .map((name) => ({ label: t(name), value: name }))
      }
    },
    {
      field: 'factory',
      label: t('工厂'),
      component: ComponentNameEnum.SELECT,
      componentProps: {
        multiple: true,
        collapseTags: true,
        filterable: true,
        options: store
          .getOptions(DictionaryCategory.FACTORY)
          .map((name) => ({ label: t(name), value: name }))
      }
    },
    {
      field: 'businessUnit',
      label: t('事业部'),
      component: ComponentNameEnum.SELECT,
      componentProps: {
        multiple: true,
        collapseTags: true,
        filterable: true,
        options: store
          .getOptions(DictionaryCategory.BUSINESS_UNIT)
          .map((name) => ({ label: t(name), value: name }))
      }
    },
    {
      field: 'productStep',
      label: t('产品阶段'),
      component: ComponentNameEnum.SELECT,
      componentProps: {
        multiple: true,
        collapseTags: true,
        filterable: true,
        options: store
          .getOptions(DictionaryCategory.PRODUCT_STEP)
          .map((name) => ({ label: t(name), value: name }))
      }
    },
    {
      field: 'productLine',
      label: t('生产线'),
      component: ComponentNameEnum.SELECT,
      componentProps: {
        multiple: true,
        collapseTags: true,
        filterable: true,
        options: store
          .getOptions(DictionaryCategory.PRODUCT_LINE)
          .map((name) => ({ label: t(name), value: name }))
      }
    },
    /*  {
      field: 'machineCategory',
      label: t('机种'),
      component: ComponentNameEnum.SELECT,
      componentProps: {
        multiple: true,
        collapseTags: true,
        filterable: true,
        options: store
          .getOptions(DictionaryCategory.MACHINE_CATEGORY)
          .map((name) => ({ label: t(name), value: name }))
      }
    }, */
    {
      field: 'machineType',
      label: t('机型'),
      component: ComponentNameEnum.SELECT,
      componentProps: {
        multiple: true,
        collapseTags: true,
        filterable: true,
        options: store
          .getOptions(DictionaryCategory.MACHINE_TYPE)
          .map((name) => ({ label: t(name), value: name }))
      }
    },
    {
      field: 'projectCode',
      label: t('项目编号'),
      component: ComponentNameEnum.INPUT,
      componentProps: {
        operate: Operators.LIKE
      }
    },
    {
      field: 'workOrderCode',
      label: t('工单号'),
      component: ComponentNameEnum.INPUT,
      componentProps: {
        operate: Operators.LIKE
      }
    }
  ]
})

const setSearchParams = (params: any) => {
  store.setFilter(params)
}
</script>
<template>
  <ContentWrap>
    <Search
      :model="initialModel"
      :schema="searchSchema"
      @search="setSearchParams"
      @reset="setSearchParams"
    />
    <TableDialog />
    <ElRow :gutter="20" justify="space-between" style="margin-top: 0.75rem">
      <ElCol :sm="24" :md="12">
        <ProductStepChart v-if="!!searchSchema.length" />
        <ElSkeleton v-else loading animated class="h-300px" />
      </ElCol>
      <ElCol :sm="24" :md="12">
        <ProductCustomerChart v-if="!!searchSchema.length" />
        <ElSkeleton v-else loading animated class="h-300px" />
      </ElCol>
      <ElCol :sm="24" :md="12">
        <ProductReasonChart v-if="!!searchSchema.length" />
        <ElSkeleton v-else loading animated class="h-300px" />
      </ElCol>
      <ElCol :sm="24" :md="12">
        <ProductBadTypeChart v-if="!!searchSchema.length" />
        <ElSkeleton v-else loading animated class="h-300px" />
      </ElCol>
    </ElRow>
  </ContentWrap>
</template>
