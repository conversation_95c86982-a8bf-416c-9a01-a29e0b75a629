import { BaseEntity, BaseNameEntity } from '../base/types'

export enum DictionaryCategory {
  CUSTOMER = 'CUSTOMER',
  BUSINESS_UNIT = 'BUSINESS_UNIT',
  FACTORY = 'FACTORY',
  PRODUCT_LINE = 'PRODUCT_LINE',
  MACHINE_CATEGORY = 'MACHINE_CATEGORY',
  MACHINE_TYPE = 'MACHINE_TYPE',
  PRODUCT_STEP = 'PRODUCT_STEP',
  UNQUALITY_TYPE = 'UNQUALITY_TYPE',
  UNQUALITY_CODE = 'UNQUALITY_CODE',
  REASON_TYPE = 'REASON_TYPE'
}

export type DictionaryOption = {
  name: string
  children?: DictionaryOption[]
}
export type Dictionary = BaseNameEntity & {
  description?: string
  category: DictionaryCategory
  options: DictionaryOption[]
  priority: number
  tree?: boolean
}
export type DictionaryRelation = BaseEntity & {
  machineType: string
  customer: string
  businessUnit?: string
}
