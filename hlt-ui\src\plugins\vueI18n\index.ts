import type { App } from 'vue'
import { createI18n } from 'vue-i18n'
import { useLocaleStoreWithOut } from '@/store/modules/locale'
import type { I18n, I18nOptions } from 'vue-i18n'
import { setHtmlPageLang } from './helper'
import { Lang } from '@/api/login/types'

export let i18n: ReturnType<typeof createI18n>

const langRecords: Record<LocaleType, LocaleType> = {
  'zh-CN': 'en',
  en: 'zh-CN'
}
const createI18nOptions = async (data: Lang[]): Promise<I18nOptions> => {
  const localeStore = useLocaleStoreWithOut()
  const locale = localeStore.getCurrentLocale
  const localeMap = localeStore.getLocaleMap
  for (const datum of data) {
    datum.message = {}
    datum.langData.forEach((item) => {
      datum.message![item.label] = (() => {
        const fn = (ctx) => {
          const { normalize: _normalize } = ctx
          const localeStore = useLocaleStoreWithOut()
          const _item = localeStore.getItem(item.label)
          if (!!_item) {
            return _normalize([_item.value.replace('\\n', '\n')])
          }
          return _normalize([item.label.replace('\\n', '\n')])
        }
        fn.source = item.value
        return fn
      })()
    })
  }
  localeStore.setData(data)
  const message1 = localeStore.getMessage(locale.lang)
  const message2 = localeStore.getMessage(langRecords[locale.lang])
  setHtmlPageLang(locale.lang)
  localeStore.setCurrentLocale({
    lang: locale.lang
    // elLocale: elLocal
  })

  return {
    legacy: false,
    locale: locale.lang,
    fallbackLocale: locale.lang,
    messages: {
      [locale.lang]: message1 as any,
      [langRecords[locale.lang]]: message2 as any
    },
    availableLocales: localeMap.map((v) => v.lang),
    sync: true,
    silentTranslationWarn: true,
    missingWarn: false,
    silentFallbackWarn: true
  }
}

export const setupI18n = async (app: App<Element>, data: Lang[]) => {
  const options = await createI18nOptions(data)
  i18n = createI18n(options) as I18n
  app.use(i18n)
}
