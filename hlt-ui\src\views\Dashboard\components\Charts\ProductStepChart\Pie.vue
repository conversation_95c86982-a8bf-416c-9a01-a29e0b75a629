<script setup lang="ts">
import { getStepChartApi } from '@/api/dashboard'
import { SearchParams } from '@/api/dashboard/types'
import { Echart } from '@/components/Echart'
import { useDashboardStore } from '@/store/modules/dashboard'
import { EChartsOption, EChartsType } from 'echarts'
import { DictionaryCategory } from '@/api/dictionary/types'
import dayjs from 'dayjs'
import { useI18n } from '@/hooks/web/useI18n'
const props = defineProps<{
  start: Date
  end: Date
  loading: { status: boolean }
}>()

const store = useDashboardStore()

const $emit = defineEmits(['update:loading'])

const echartRef = ref<{ getECharts: () => EChartsType } | null>(null)
const { t } = useI18n()
$emit('update:loading', { status: true })
watchEffect(async () => {
  const { data } = await getStepChartApi(
    {
      ...store.filter,
      monthRange: store.datepicker ? [props.start, props.end] : (store.filter as any)?.monthRange
    } as SearchParams,
    true
  )
  setTimeout(() => {
    modifyOptions.value = data
  }, 100)
})

const options = ref<EChartsOption>({
  tooltip: {
    trigger: 'item',
    formatter: (params) => `${params.marker} ${t(params.name)}: ${params.value}`
  },
  legend: {
    formatter: (name) => t(name)
  },
  color: [
    'rgb(0, 157, 255)',
    'rgb(34, 228, 255)',
    'rgb(59, 255, 208)',
    'rgb(4, 227, 138)',
    'rgb(157, 255, 134)',
    'rgb(254, 229, 136)'
  ]
})
const modifyOptions = computed<Record<string, any>>({
  get: () => options.value,
  set: ({
    data,
    total
  }: {
    data: { month: string; type: string; value: string }[]
    total: number
  }) => {
    const stepFilter = !!store.filter?.productStep?.length
      ? new Set(store.filter.productStep)
      : undefined
    let legend = store.getOptions(DictionaryCategory.PRODUCT_STEP)
    if (stepFilter) {
      legend = store
        .getOptions(DictionaryCategory.PRODUCT_STEP)
        .filter((step) => stepFilter.has(step))
    }
    options.value.series = [
      {
        type: 'pie',
        radius: '65%',
        center: ['50%', '50%'],
        selectedMode: 'single',
        data: legend.map((name) => {
          const value = parseInt(data.find((datum) => datum.type === name)?.value ?? '0', 10)
          return {
            label: { formatter: total === 0 ? '0%' : `${((value * 100) / total).toFixed(2)}%` },
            value,
            name
          }
        }),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
    const _echartRef = unref(echartRef)
    if (!!_echartRef) {
      const echarts = _echartRef.getECharts()
      echarts.setOption(options.value, true)
    }
  }
})
watch(
  () => echartRef.value,
  (echartRef) => {
    if (echartRef) {
      const echarts = echartRef.getECharts()
      echarts.on('click', function ({ name, value }) {
        if (value === 0) {
          return
        }
        store.seriesFilter = { productStep: [name!] }
        if (store.datepicker) {
          store.monthRange = [
            dayjs(props.start).startOf('M').toDate(),
            dayjs(props.end).endOf('M').toDate()
          ]
        } else {
          store.monthRange = (store.filter as any).monthRange
        }
        store.setTableDialogVisible(true)
      })
    }
  },
  { deep: true }
)
</script>
<template>
  <Echart
    ref="echartRef"
    v-if="options.series"
    :options="options as EChartsOption"
    height="18.75rem"
  />
</template>
